<template>
  <div 
    class="course-card"
    :class="{ 
      'completed-course': course.completion_percentage === 100,
      'list-view': viewMode === 'list'
    }"
    @click="handleCourseClick"
  >
    <div class="card-header">
      <div class="course-status">
        <div class="status-icon" :class="getStatusClass(course.completion_percentage)">
          <i class="fa" :class="getStatusIcon(course.completion_percentage)"></i>
        </div>
        <span class="status-label">{{ getStatusLabel(course.completion_percentage) }}</span>
      </div>
      <div class="progress-badge">
        {{ Math.round(course.completion_percentage || 0) }}%
      </div>
    </div>

    <div class="course-image" v-if="showImage">
      <img v-if="course.banner_full_path" :src="course.banner_full_path" :alt="course.title" class="course-img" />
      <div v-else class="course-placeholder">
        <i class="fa fa-book"></i>
      </div>
      
      <div class="course-overlay" v-if="course.level">
        <div class="course-level">
          <span class="level-badge">{{ course.level || 'Beginner' }}</span>
        </div>
      </div>
    </div>

    <div class="card-content">
      <h3 class="card-title">{{ course.title_short || course.title }}</h3>
      <p class="card-description" v-if="course.description_short || course.description">
        {{ course.description_short || course.description }}
      </p>
      
      <CourseProgressBar 
        :completion-percentage="course.completion_percentage || 0"
        :completed-lessons="course.completed_lessons || 0"
        :total-lessons="course.total_lessons || 0"
        v-if="showProgress"
      />

      <div class="course-meta" v-if="showMeta">
        <div class="meta-item">
          <i class="fa fa-calendar"></i>
          <span>{{ formatDate(course.last_activity || course.created_at) }}</span>
        </div>
        <div class="meta-item">
          <i class="fa fa-clock-o"></i>
          <span>{{ course.duration || 'Self-paced' }}</span>
        </div>
      </div>
    </div>

    <div class="card-footer" v-if="showActions">
      <router-link 
        v-if="(course.completion_percentage || 0) < 100"
        :to="getCourseRoute(course)"
        class="continue-btn"
      >
        <i class="fa fa-play"></i>
        <span>{{ continueButtonText }}</span>
      </router-link>

      <button 
        v-else-if="course.certificate_eligible"
        @click.stop="handleCertificateDownload"
        class="download-cert-btn"
      >
        <i class="fa fa-download"></i>
        <span>Download Certificate</span>
      </button>

      <router-link 
        v-else
        :to="getCourseRoute(course)"
        class="review-btn"
      >
        <i class="fa fa-eye"></i>
        <span>Review Course</span>
      </router-link>
    </div>

    <div v-if="(course.completion_percentage || 0) === 100" class="completion-ring">
      <div class="progress-circle">
        <svg viewBox="0 0 36 36" class="circular-chart">
          <path class="circle-bg"
            d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
          />
          <path class="circle"
            stroke-dasharray="100, 100"
            d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
          />
        </svg>
        <div class="progress-text">
          <i class="fa fa-check"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import CourseProgressBar from './CourseProgressBar.vue';

const props = defineProps({
  course: {
    type: Object,
    required: true
  },
  viewMode: {
    type: String,
    default: 'grid'
  },
  showImage: {
    type: Boolean,
    default: true
  },
  showProgress: {
    type: Boolean,
    default: true
  },
  showMeta: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  continueButtonText: {
    type: String,
    default: 'Continue Learning'
  },
  routeName: {
    type: String,
    default: 'AwarenessCoursePreview'
  }
});

const emit = defineEmits(['course-click', 'certificate-download']);

const {
  getStatusIcon,
  getStatusLabel,
  getStatusClass,
  formatDate,
  canAccessCourse,
  downloadCourseCertificate
} = useAwarenessActions();

const handleCourseClick = () => {
  emit('course-click', props.course);
};

const handleCertificateDownload = async () => {
  try {
    await downloadCourseCertificate(props.course._id);
    emit('certificate-download', props.course);
  } catch (error) {
    console.error('Error downloading certificate:', error);
  }
};

const getCourseRoute = (course) => {
  return { name: props.routeName, params: { course_id: course._id } };
};
</script>

<style scoped>
.course-card {
  background: white;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.course-card.list-view {
  display: flex;
  align-items: center;
  padding: 1.5rem;
}

.course-card.list-view .course-image {
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  margin-right: 2rem;
}

.course-card.list-view .card-content {
  flex: 1;
  padding: 0;
}

.course-card.list-view .card-footer {
  padding: 0;
  margin-left: 2rem;
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 0;
}

.course-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.status-icon.completed {
  background: #10b981;
  color: white;
}

.status-icon.in-progress {
  background: #3b82f6;
  color: white;
}

.status-icon.not-started {
  background: #6b7280;
  color: white;
}

.status-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.progress-badge {
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
}

.course-image {
  width: 100%;
  height: 220px;
  position: relative;
  overflow: hidden;
}

.course-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 3rem;
}

.course-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.level-badge {
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.card-description {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.card-footer {
  padding: 0 1.5rem 1.5rem;
}

.continue-btn, .download-cert-btn, .review-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.continue-btn {
  background: #3b82f6;
  color: white;
}

.continue-btn:hover {
  background: #2563eb;
  color: white;
}

.download-cert-btn {
  background: #10b981;
  color: white;
}

.download-cert-btn:hover {
  background: #059669;
}

.review-btn {
  background: #6b7280;
  color: white;
}

.review-btn:hover {
  background: #4b5563;
  color: white;
}

.completion-ring {
  position: absolute;
  top: 1rem;
  left: 1rem;
  width: 48px;
  height: 48px;
}

.progress-circle {
  position: relative;
  width: 100%;
  height: 100%;
}

.circular-chart {
  width: 100%;
  height: 100%;
}

.circle-bg {
  fill: none;
  stroke: #e5e7eb;
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke: #10b981;
  stroke-width: 2;
  stroke-linecap: round;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #10b981;
  font-size: 1rem;
  font-weight: 600;
}
</style>
