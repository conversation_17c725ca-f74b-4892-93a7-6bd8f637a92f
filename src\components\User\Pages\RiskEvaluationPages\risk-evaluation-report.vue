<template> 
    <div class="risk-evaluation-page">
        <div class="container">
            <!-- Loading States -->
            <div class="loading-section" v-if="risk_evaluation == null">
                <div class="loading-card" v-if="loadingStep === 0">
                    <div class="loading-icon">
                        <i class="fa fa-cog fa-spin"></i>
                    </div>
                    <h4 class="loading-title">Processing Report...</h4>
                    <p class="loading-subtitle">Analyzing your risk evaluation data</p>
                </div>

                <div class="loading-card" v-if="loadingStep === 1">
                    <div class="loading-icon">
                        <i class="fa fa-clipboard-list fa-spin"></i>
                    </div>
                    <h4 class="loading-title">Processing Your Submitted Answers...</h4>
                    <div class="progress-container">
                        <div class="progress-bar" :style="{width: progress+'%'}"></div>
                    </div>
                </div>

                <div class="loading-card" v-if="loadingStep === 2">
                    <div class="loading-icon">
                        <i class="fa fa-shield-alt fa-spin"></i>
                    </div>
                    <h4 class="loading-title">Evaluating Risk For Specific Sector...</h4>
                    <div class="progress-container">
                        <div class="progress-bar" :style="{width: progress+'%'}"></div>
                    </div>
                </div>

                <div class="loading-card" v-if="loadingStep === 3">
                    <div class="loading-icon">
                        <i class="fa fa-chart-line fa-spin"></i>
                    </div>
                    <h4 class="loading-title">Measuring the Risk...</h4>
                    <div class="progress-container">
                        <div class="progress-bar" :style="{width: progress+'%'}"></div>
                    </div>
                </div>

                <div class="loading-card" v-if="loadingStep === 4">
                    <div class="loading-icon">
                        <i class="fa fa-file-chart-pie fa-spin"></i>
                    </div>
                    <h4 class="loading-title">Generating Visual Report of Risk Evaluation</h4>
                    <div class="progress-container">
                        <div class="progress-bar" :style="{width: progress+'%'}"></div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content" v-if="risk_evaluation != null" id="risk-report-content">
                <!-- Header -->
                <div class="report-header">
                    <div class="header-navigation">
                        <router-link :to="{name: 'RiskEvaluation'}" class="back-btn">
                            <i class="fa fa-arrow-left"></i>
                            <span>Back to Dashboard</span>
                        </router-link>
                    </div>

                    <div class="header-content">
                        <div class="project-info">
                            <h1 class="project-title">{{ risk_evaluation.evaluation.project.name }}</h1>
                            <div class="project-meta">
                                <div class="analysis-type">
                                    <span class="type-badge" v-if="risk_evaluation.evaluation.category === 'et'">
                                        <i class="fa fa-shield-alt"></i>
                                        General Risk Evaluation
                                    </span>
                                    <span class="type-badge type-specific" v-if="risk_evaluation.evaluation.category === 'eta'">
                                        <i class="fa fa-industry"></i>
                                        Specific Industry Risk Evaluation
                                    </span>
                                    <span class="type-badge type-non-tech" v-if="risk_evaluation.evaluation.category === 'nt'">
                                        <i class="fa fa-user-shield"></i>
                                        Non Tech Risk Evaluation
                                    </span>
                                    <span class="type-badge type-fair" v-if="risk_evaluation.evaluation.category === 'fd'">
                                        <i class="fa fa-balance-scale"></i>
                                        General Fair Decision
                                    </span>
                                    <span class="type-badge type-specific" v-if="risk_evaluation.evaluation.category === 'eta-fd'">
                                        <i class="fa fa-industry"></i>
                                        Specific Industry Fair Decision
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="header-actions">
                            <button @click="downloadPDF" class="download-btn">
                                <i class="fa fa-download"></i>
                                <span>Download Report</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Completion Certificate -->
                <div class="completion-section" v-if="risk_evaluation.riskReport.riskPercentage.No === 100 && Subscription.package_price !== 0">
                    <div class="certificate-card">
                        <div class="certificate-icon">
                            <i class="fa fa-certificate"></i>
                        </div>
                        <div class="certificate-content">
                            <h3 class="certificate-title">Congratulations!</h3>
                            <p class="certificate-message">You have successfully completed the Risk evaluation by resolving the mitigation strategies.</p>
                            <button @click="downloadCertificate" class="certificate-btn">
                                <i class="fa fa-award"></i>
                                Download Certificate
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Risk Overview -->
                <div class="risk-overview">
                    <div class="overview-card">
                        <h3 class="overview-title">
                            <i class="fa fa-chart-pie"></i>
                            Risk Level Distribution
                        </h3>
                        <div class="risk-stats">
                            <div class="risk-stat">
                                <span class="stat-label">No Risk</span>
                                <span class="stat-value no-risk">{{ risk_evaluation.riskReport.riskPercentage.No }}%</span>
                            </div>
                            <div class="risk-stat">
                                <span class="stat-label">Low Risk</span>
                                <span class="stat-value low-risk">{{ risk_evaluation.riskReport.riskPercentage.Low }}%</span>
                            </div>
                            <div class="risk-stat">
                                <span class="stat-label">Limited Risk</span>
                                <span class="stat-value limited-risk">{{ risk_evaluation.riskReport.riskPercentage.Limited }}%</span>
                            </div>
                            <div class="risk-stat">
                                <span class="stat-label">High Risk</span>
                                <span class="stat-value high-risk">{{ risk_evaluation.riskReport.riskPercentage.High }}%</span>
                            </div>
                        </div>
                        <!-- Risk Level Legend -->
                        <div class="risk-legend">
                            <h5 class="legend-title">Risk Level Guide</h5>
                            <div class="legend-items">
                                <div class="legend-item">
                                    <span class="risk-badge no-risk">No Risk</span>
                                </div>
                                <div class="legend-item">
                                    <span class="risk-badge low-risk">Low Risk</span>
                                </div>
                                <div class="legend-item">
                                    <span class="risk-badge limited-risk">Limited Risk</span>
                                </div>
                                <div class="legend-item">
                                    <span class="risk-badge high-risk">High Risk</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Analysis Sections -->
                <div class="analysis-sections">
                    <!-- ET Category -->
                    <div class="section-grid" v-if="risk_evaluation.evaluation.category === 'et'">
                        <div class="section-item" v-for="(section,index) in risk_evaluation.riskReport.sections" :key="index">
                            <div class="section-card">
                                <div class="section-header">
                                    <h4 class="section-title">{{ section.title }}</h4>
                                </div>
                                <div class="section-content">
                                    <div v-if="section.risk_report === undefined" class="empty-section">
                                        <div class="empty-icon">
                                            <i class="fa fa-exclamation-triangle"></i>
                                        </div>
                                        <p class="empty-message">You didn't answer <strong>{{ section.title }}</strong> section questions.</p>
                                        <a class="answer-btn" :href="'/portal/risk-evaluation/et-questions?ev='+risk_evaluation.evaluation._id+'&cgr='+index">
                                            <i class="fa fa-edit"></i>
                                            View Questions
                                        </a>
                                    </div>
                                    <div v-else class="question-grid">
                                        <div v-for="(rl,qIndex) in section.risk_report.questionRiskLevel" :key="qIndex"
                                             @click="OpenThisQuestion(section,qIndex,rl)"
                                             class="question-bubble"
                                             :class="'risk-' + rl.toLowerCase()">
                                            <span class="question-number">{{ qIndex }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ETA Category -->
                    <div class="single-section" v-if="risk_evaluation.evaluation.category === 'eta'">
                        <div class="section-card">
                            <div class="section-header">
                                <h4 class="section-title">
                                    <i class="fa fa-shield-alt"></i>
                                    Risk Evaluation
                                </h4>
                            </div>
                            <div class="section-content">
                                <div class="question-grid centered">
                                    <div v-for="(rl,index) in risk_evaluation.riskReport.risk_report.questionRiskLevel" :key="index"
                                         @click="OpenThisQuestion('eta',index,rl)"
                                         class="question-bubble large"
                                         :class="'risk-' + rl.toLowerCase()">
                                        <span class="question-number">{{ index }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ETA-FD Category -->
                    <div class="section-grid" v-if="risk_evaluation.evaluation.category === 'eta-fd'">
                        <div class="section-item" v-for="(section,index) in risk_evaluation.riskReport.sections" :key="index"
                             :class="is_last_even(index)">
                            <div class="section-card">
                                <div class="section-header">
                                    <h4 class="section-title">{{ section.title }}</h4>
                                </div>
                                <div class="section-content">
                                    <div v-if="section.risk_report === undefined" class="empty-section">
                                        <div class="empty-icon">
                                            <i class="fa fa-exclamation-triangle"></i>
                                        </div>
                                        <p class="empty-message">You didn't answer <strong>{{ section.title }}</strong> section questions.</p>
                                        <a class="answer-btn" :href="'/portal/risk-evaluation/et-questions?ev='+risk_evaluation.evaluation._id+'&cgr='+index">
                                            <i class="fa fa-edit"></i>
                                            View Questions
                                        </a>
                                    </div>
                                    <div v-else class="question-grid">
                                        <div v-for="(rl,rlIndex) in section.risk_report.questionRiskLevel" :key="rlIndex"
                                             @click="OpenThisQuestion('eta-fd',rlIndex, rl, section)"
                                             class="question-bubble"
                                             :class="'risk-' + rl.toLowerCase()">
                                            <span class="question-number">{{ rlIndex }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- NT Category -->
                    <div class="single-section" v-if="risk_evaluation.evaluation.category === 'nt'">
                        <div class="section-card">
                            <div class="section-header">
                                <h4 class="section-title">
                                    <i class="fa fa-user-shield"></i>
                                    Risk Evaluation
                                </h4>
                            </div>
                            <div class="section-content">
                                <div class="question-grid centered">
                                    <div v-for="(rl,index) in risk_evaluation.riskReport.risk_report.questionRiskLevel" :key="index"
                                         @click="OpenThisQuestion('nt',index,rl)"
                                         class="question-bubble large"
                                         :class="'risk-' + rl.toLowerCase()">
                                        <span class="question-number">{{ index }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FD Category -->
                    <div class="single-section" v-if="risk_evaluation.evaluation.category === 'fd'">
                        <div class="section-card">
                            <div class="section-header">
                                <h4 class="section-title">
                                    <i class="fa fa-balance-scale"></i>
                                    General Fair Decision
                                </h4>
                            </div>
                            <div class="section-content">
                                <div class="question-grid centered">
                                    <div v-for="(rl,index) in risk_evaluation.riskReport.risk_report.questionRiskLevel" :key="index"
                                         @click="OpenThisQuestion('fd',index,rl)"
                                         class="question-bubble large"
                                         :class="'risk-' + rl.toLowerCase()">
                                        <span class="question-number">{{ index }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Results -->
                <div class="results-section">
                    <div class="results-grid">
                        <!-- Mitigation Strategies -->
                        <div class="mitigation-card">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fa fa-lightbulb"></i>
                                    Risk Level & Mitigation Strategies
                                </h3>
                            </div>
                            <div class="card-content">
                                <div v-if="Subscription.package_price !== 0" class="mitigation-content" v-html="ReportGPT"></div>
                                <div v-else class="premium-notice">
                                    <div class="premium-icon">
                                        <i class="fa fa-crown"></i>
                                    </div>
                                    <h4 class="premium-title">Premium Feature</h4>
                                    <p class="premium-description">
                                        Mitigation Strategies is a premium feature. 
                                        You must purchase any premium package to have all premium features.
                                    </p>
                                    <router-link :to="{name: 'Pricing'}" class="premium-btn">
                                        <i class="fa fa-arrow-up"></i>
                                        Choose Plan
                                    </router-link>
                                </div>
                            </div>
                        </div>

                        <!-- Visual Chart -->
                        <div class="chart-card" v-if="chart != null">
                            <div class="card-header">
                                <h3 class="card-title">
                                    <i class="fa fa-chart-pie"></i>
                                    Risk Distribution
                                </h3>
                            </div>
                            <div class="card-content">
                                <div class="chart-container">
                                    <Pie :data="chart" :options="chartOptions"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Modal -->
        <div class="modal fade" id="singleQuestionModal">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <form id="single_question_form" @submit.prevent="updateQuestionAnswer" class="w-100">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" v-if="singleQuestion === null">
                                Processing... <i class="fa fa-fw fa-spin fa-spinner"></i>
                            </h1>
                            <h1 class="modal-title fs-5" v-if="singleQuestion !== null">
                                <strong>{{ singleQuestion.question }}</strong>
                            </h1>
                        </div>
                        <div class="modal-body">
                            <p v-if="singleQuestion === null" class="alert alert-info">
                                Fetching Information. Please wait... <i class="fa fa-fw fa-spin fa-spinner"></i>
                            </p>
                            <div class="eachQuestions w-100 bg-white" v-if="singleQuestion !== null">
                                <div class="w-100">
                                    <div class="row">
                                        <div class="col-xl-12 mt-3">
                                            <div class="w-100">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <ul class="list-unstyled px-2 px-lg-5">
                                                            <li v-if="singleQuestion.option_1.title !== null" :class="{'pointer-event-none': singleQuestionMitigation == null}">
                                                                <template v-if="singleQuestion.type === 'Regular'">
                                                                    <p class="text-secondary m-0 p-0"><span v-html="singleQuestion.option_1.title"></span></p>
                                                                </template>
                                                                <template v-if="singleQuestion.type === 'Multiple_Answers'">
                                                                    <p class="text-dark m-0 p-0"><strong v-html="singleQuestion.option_1.title"></strong></p>
                                                                    <p class="text-secondary m-0 p-0"><span v-html="singleQuestion.option_1.desc"></span></p>
                                                                </template>
                                                                <div class="w-100 mt-2 custom-radio-checkbox">
                                                                    <div class="form-check form-check-inline ps-0">
                                                                        <input class="form-check-input-custom"
                                                                               @change="no_risk_confirm_auto"
                                                                               :checked="singleQuestion.option_1.answer == '0'"
                                                                               :name="singleQuestion._id+'_q1'" type="radio" :id="'qCheck1Y'" value="0" required>
                                                                        <label class="form-check-label" :for="'qCheck1Y'">Yes</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline ps-0">
                                                                        <input class="form-check-input-custom"
                                                                               @change="no_risk_confirm_auto"
                                                                               :checked="singleQuestion.option_1.answer == singleQuestion.option_1.risk_value"
                                                                               :name="singleQuestion._id+'_q1'" type="radio" :id="'qCheck1N'" :value="singleQuestion.option_1.risk_value" required>
                                                                        <label class="form-check-label" :for="'qCheck1N'">No</label>
                                                                    </div>
                                                                    <div class="form-check form-check-inline ps-0">
                                                                        <input class="form-check-input-custom"
                                                                               @change="no_risk_confirm_auto"
                                                                               :checked="singleQuestion.option_1.answer == '-1'"
                                                                               :name="singleQuestion._id+'_q1'" type="radio" :id="'qCheck1NA'" value="-1" required>
                                                                        <label class="form-check-label" :for="'qCheck1NA'">NA</label>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <template v-if="singleQuestion.type === 'Multiple_Answers'">
                                                                <li class="mt-5" v-if="singleQuestion.option_2.title !== null" :class="{'pointer-event-none': singleQuestionMitigation == null}">
                                                                    <p class="text-dark m-0 p-0"><strong v-html="singleQuestion.option_2.title"></strong></p>
                                                                    <p class="text-secondary m-0 p-0"><span v-html="singleQuestion.option_2.desc"></span></p>
                                                                    <div class="w-100 mt-2 custom-radio-checkbox">
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_2.answer == '0'"
                                                                                   :name="singleQuestion._id+'_q2'" type="radio" :id="'qCheck2Y'" value="0" required>
                                                                            <label class="form-check-label" :for="'qCheck2Y'">Yes</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_2.answer == singleQuestion.option_2.risk_value"
                                                                                   :name="singleQuestion._id+'_q2'" type="radio" :id="'qCheck2N'" :value="singleQuestion.option_2.risk_value" required>
                                                                            <label class="form-check-label" :for="'qCheck2N'">No</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_2.answer == '-1'"
                                                                                   :name="singleQuestion._id+'_q2'" type="radio" :id="'qCheck2NA'" value="-1" required>
                                                                            <label class="form-check-label" :for="'qCheck2NA'">NA</label>
                                                                        </div>
                                                                    </div>
                                                                </li>
                                                                <li class="mt-5" v-if="singleQuestion.option_3.title !== null" :class="{'pointer-event-none': singleQuestionMitigation == null}">
                                                                    <p class="text-dark m-0 p-0"><strong v-html="singleQuestion.option_3.title"></strong></p>
                                                                    <p class="text-secondary m-0 p-0"><span v-html="singleQuestion.option_3.desc"></span></p>
                                                                    <div class="w-100 mt-2 custom-radio-checkbox">
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_3.answer == '0'"
                                                                                   :name="singleQuestion._id+'_q3'" type="radio" :id="'qCheck3Y'" value="0" required>
                                                                            <label class="form-check-label" :for="'qCheck3Y'">Yes</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_3.answer == singleQuestion.option_3.risk_value"
                                                                                   :name="singleQuestion._id+'_q3'" type="radio" :id="'qCheck3N'" :value="singleQuestion.option_3.risk_value" required>
                                                                            <label class="form-check-label" :for="'qCheck3N'">No</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_3.answer == '-1'"
                                                                                   :name="singleQuestion._id+'_q3'" type="radio" :id="'qCheck3NA'" value="-1" required>
                                                                            <label class="form-check-label" :for="'qCheck3NA'">NA</label>
                                                                        </div>
                                                                    </div>
                                                                </li>
                                                                <li class="mt-5" v-if="singleQuestion.option_4.title !== null" :class="{'pointer-event-none': singleQuestionMitigation == null}">
                                                                    <p class="text-dark m-0 p-0"><strong v-html="singleQuestion.option_4.title"></strong></p>
                                                                    <p class="text-secondary m-0 p-0"><span v-html="singleQuestion.option_4.desc"></span></p>
                                                                    <div class="w-100 mt-2 custom-radio-checkbox">
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_4.answer == '0'"
                                                                                   :name="singleQuestion._id+'_q4'" type="radio" :id="'qCheck4Y'" value="0" required>
                                                                            <label class="form-check-label" :for="'qCheck4Y'">Yes</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_4.answer == singleQuestion.option_4.risk_value"
                                                                                   :name="singleQuestion._id+'_q4'" type="radio" :id="'qCheck4N'" :value="singleQuestion.option_4.risk_value" required>
                                                                            <label class="form-check-label" :for="'qCheck4N'">No</label>
                                                                        </div>
                                                                        <div class="form-check form-check-inline ps-0">
                                                                            <input class="form-check-input-custom"
                                                                                   @change="no_risk_confirm_auto"
                                                                                   :checked="singleQuestion.option_4.answer == '-1'"
                                                                                   :name="singleQuestion._id+'_q4'" type="radio" :id="'qCheck4NA'" value="-1" required>
                                                                            <label class="form-check-label" :for="'qCheck4NA'">NA</label>
                                                                        </div>
                                                                    </div>
                                                                </li>
                                                            </template>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <template v-if="mitigationLoading === 1">
                            <div class="w-100 text-center">
                                <p class="alert alert-primary">Calculating Mitigation Strategies... <i class="fa fa-fw fa-spin fa-spinner"></i></p>
                            </div>
                        </template>
                        <template v-if="mitigationLoading === 0">
                            <div class="modal-header border-top bg-light">
                                <h1 class="modal-title fs-5">Risk Level & Mitigation Strategies</h1>
                            </div>
                            <div class="modal-body" v-if="mitigationLoading === 0">
                                <p v-if="singleQuestionMitigation == null" class="alert alert-primary">No Mitigation Strategies has been found.</p>
                                <div class="alert alert-warning" v-if="singleQuestionMitigation != null && singleQuestionRisk !== 'No'">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="1" id="no_risk_confirm" @change="no_risk_confirm">
                                        <label class="form-check-label" for="no_risk_confirm">
                                            <strong>I confirm that I have addressed the issues of the above questions</strong>
                                        </label>
                                    </div>
                                </div>
                                <div class="w-100 p-3" style="height: 350px;overflow: auto;" v-if="singleQuestionMitigation != null">
                                    <h6 v-if="singleQuestionMitigation != null" v-html="singleQuestionMitigation.mitigation"></h6>
                                </div>
                            </div>
                        </template>
                        <div class="modal-footer d-flex justify-content-between align-items-center">
                            <div class="d-inline-block">
                                <input type="hidden" name="domain" :value="'domain_'+singleQuestionDomain">
                                <input type="hidden" name="evaluation_id" :value="evaluation_id">
                                <button type="button" class="btn btn-sm btn-secondary rounded-pill px-3 me-2" data-bs-dismiss="modal">Close</button>
                            </div>
                            <div class="d-inline-block">
                                <button type="button" class="btn btn-sm btn-warning rounded-pill px-3 me-2" @click="OpenThisQuestionChatGPT(singleQuestion)">Mitigation Strategy</button>
                                <button type="submit" class="btn btn-sm btn-success rounded-pill px-3">Save Changes</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import {Chart as ChartJS, ArcElement, Tooltip, Legend} from 'chart.js'
import {Pie} from 'vue-chartjs'
import Swal from "sweetalert2";

ChartJS.register(ArcElement, Tooltip, Legend)

export default {
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            Subscription: JSON.parse(localStorage.getItem('Subscription')),
            loading: 1,
            mitigationLoading: null,
            loadingStep: 0,
            progress: 0,
            evaluation_id: this.$route.params.evaluation_id,
            risk_evaluation: null,
            chart: null,
            chartOptions: null,
            singleQuestion: null,
            singleQuestionRisk: null,
            singleQuestionIndex: null,
            singleQuestionMitigation: null,
            singleQuestionDomain: null,
            singleQuestionModal: null,
            singleQuestionChatGPTModal: null,
            ReportGPT: null
        }
    },
    components: {Pie},
    watch: {},
    methods: {
        downloadPDF: function() {
            const reportElement = document.getElementById('risk-report-content');
            const projectName = this.risk_evaluation.evaluation.project.name || 'Risk-Report';
            
            // Hide interactive elements for PDF
            const interactiveElements = reportElement.querySelectorAll('.question-bubble, .back-btn, .download-btn, .premium-btn, .answer-btn, .certificate-btn');
            interactiveElements.forEach(el => {
                el.style.display = 'none';
            });
            
            // Print the page
            const originalTitle = document.title;
            document.title = `${projectName} - Risk Analysis Report`;
            
            window.print();
            
            // Restore interactive elements after printing
            setTimeout(() => {
                interactiveElements.forEach(el => {
                    el.style.display = '';
                });
                document.title = originalTitle;
            }, 1000);
        },

        showLoading: function (step) {
            if (step <= 4) {
                this.progress = 0;
                this.loadingStep = step;
                setTimeout(() => {
                    this.progress = 30;
                    setTimeout(() => {
                        this.progress = 50;
                        setTimeout(() => {
                            this.progress = 75;
                            setTimeout(() => {
                                this.progress = 90;
                                setTimeout(() => {
                                    this.progress = 100;
                                    setTimeout(() => {
                                        this.showLoading(step + 1);
                                    }, 1000)
                                }, 1000)
                            }, 1000)
                        }, 1000)
                    }, 1000)
                }, 1000)
            }
        },

        is_last_even(idx) {
            const last = idx + 1;
            if (last === this.risk_evaluation.riskReport.sections.length) {
                if (idx % 2 === 0) {
                    return 'col-lg-12';
                } else {
                    return 'col-lg-6';
                }
            } else {
                return 'col-lg-6';
            }
        },

        pieClicked: function (event, section) {
            console.log(event, section);
        },

        evaluation_report: function () {
            const THIS = this;
            THIS.loading = 1;
            THIS.risk_evaluation = null;
            THIS.ReportGPT = null;
            ApiService.POST(ApiRoutes.UserEvaluationReport, {evaluation_id: THIS.evaluation_id}, function (res) {
                THIS.loading = 0;
                if (res.status === 200) {
                    THIS.risk_evaluation = res.risk_evaluation;
                    THIS.ReportGPT = res.risk_evaluation.riskReport.chatGPT;

                    // The Pie Chart
                    THIS.chart = {
                        labels: [
                            'Low (' + res.risk_evaluation.riskReport.riskPercentage.Low + '%)',
                            'Limited (' + res.risk_evaluation.riskReport.riskPercentage.Limited + '%)',
                            'High (' + res.risk_evaluation.riskReport.riskPercentage.High + '%)',
                            'No (' + res.risk_evaluation.riskReport.riskPercentage.No + '%)'
                        ],
                        datasets: [
                            {
                                backgroundColor: ['#10b981', '#f59e0b', '#ef4444', '#e5e7eb'],
                                borderColor: ['#059669', '#d97706', '#dc2626', '#d1d5db'],
                                borderWidth: [2, 2, 2, 2],
                                data: [
                                    res.risk_evaluation.riskReport.riskPercentage.Low,
                                    res.risk_evaluation.riskReport.riskPercentage.Limited,
                                    res.risk_evaluation.riskReport.riskPercentage.High,
                                    res.risk_evaluation.riskReport.riskPercentage.No
                                ]
                            }
                        ],
                    };
                    THIS.chartOptions = {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    font: {
                                        size: 12,
                                        weight: 600
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: 'white',
                                bodyColor: 'white',
                                borderColor: 'rgba(255, 255, 255, 0.2)',
                                borderWidth: 1
                            }
                        },
                        onClick: THIS.pieClicked
                    }
                }
            })
        },

        OpenThisQuestion: function (domain, index, risk_level, section = null) {
            const THIS = this;
            THIS.singleQuestionRisk = risk_level;
            THIS.mitigationLoading = null;
            THIS.singleQuestionMitigation = null;
            if (domain === 'eta' || domain === 'nt' || domain === 'fd' || domain === 'eta-fd') {
                THIS.singleQuestionDomain = domain;
            } else {
                THIS.singleQuestionDomain = domain.uid;
            }

            let param = {
                group: THIS.singleQuestionDomain,
                q_index: index,
                evaluation_id: this.evaluation_id,
            }
            if (domain === 'eta-fd') {
                param['fd_index'] = 'domain_' + section.parent_id + '_' + section.uid
            }

            THIS.singleQuestion = null;
            THIS.singleQuestionIndex = index;
            THIS.openSingleQuestion();
            ApiService.POST(ApiRoutes.UserEvaluationQuestionSingle, param, function (res) {
                if (res.status === 200) {
                    THIS.singleQuestion = res.data;
                }
            })
        },

        OpenThisQuestionChatGPT: function (singleQuestion) {
            if (this.Subscription.package_price === 0) {
                Swal.fire({
                    title: 'Premium Feature',
                    text: "You must purchase any premium package to have all premium features.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#0d9f56',
                    cancelButtonColor: '#777777',
                    confirmButtonText: 'Go to Pricing',
                    cancelButtonText: 'Close'
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.$router.push({name: 'Pricing'})
                    }
                });
            } else {
                const THIS = this;
                const param = {
                    _id: singleQuestion._id,
                    evaluation_id: this.evaluation_id,
                    q_index: this.singleQuestionIndex,
                    risk: this.singleQuestionRisk,
                }
                if (singleQuestion.category === 'eta-fd') {
                    param['fd_index'] = 'domain_' + singleQuestion.sector + '_' + singleQuestion.sub_sector
                }
                THIS.mitigationLoading = 1;
                ApiService.POST(ApiRoutes.UserEvaluationQuestionSingleChatGPT, param, function (res) {
                    THIS.mitigationLoading = 0;
                    if (res.status === 200) {
                        THIS.singleQuestion = res.data;
                        THIS.singleQuestionMitigation = res.data.mitigation;
                    }
                })
            }
        },

        updateQuestionAnswer: function () {
            const THIS = this;
            const object = {};
            const formData = new FormData(document.getElementById('single_question_form'));
            formData.forEach(function (value, key) {
                object[key] = value;
            });
            let domain = '';
            if (THIS.singleQuestionDomain === 'nt') {
                domain = 'nt';
            } else if (THIS.singleQuestionDomain === 'fd') {
                domain = 'fd';
            } else if (THIS.singleQuestionDomain === 'eta-fd') {
                domain = 'eta-fd';
            } else {
                domain = 'domain_' + THIS.singleQuestionDomain;
            }
            const param = {
                answers: object,
                domain: domain,
                evaluation_id: THIS.evaluation_id
            }
            if (this.singleQuestion.category === 'eta-fd') {
                param['fd_index'] = 'domain_' + this.singleQuestion.sector + '_' + this.singleQuestion.sub_sector
            }
            ApiService.POST(ApiRoutes.UserEvaluationQuestionSingleUpdate, param, function (res) {
                if (res.status === 200) {
                    THIS.closeSingleQuestion();
                    THIS.showLoading(1);
                    THIS.evaluation_report();
                }
            })
        },

        openSingleQuestion: function () {
            this.singleQuestionModal = new bootstrap.Modal(document.getElementById('singleQuestionModal'));
            this.singleQuestionModal.show();
        },

        closeSingleQuestion: function () {
            this.singleQuestionModal.hide();
        },

        no_risk_confirm: function () {
            const no_risk_confirm = document.getElementById('no_risk_confirm');
            if (no_risk_confirm.checked) {
                if (this.singleQuestion.option_1.answer !== undefined) {
                    this.singleQuestion.option_1.answer = '0';
                }
                if (this.singleQuestion.option_2.answer !== undefined) {
                    this.singleQuestion.option_2.answer = '0';
                }
                if (this.singleQuestion.option_3.answer !== undefined) {
                    this.singleQuestion.option_3.answer = '0';
                }
                if (this.singleQuestion.option_4.answer !== undefined) {
                    this.singleQuestion.option_4.answer = '0';
                }
            } else {
                if (this.singleQuestion.option_1.answer !== undefined) {
                    this.singleQuestion.option_1.answer = this.singleQuestion.option_1.risk_value;
                }
                if (this.singleQuestion.option_2.answer !== undefined) {
                    this.singleQuestion.option_2.answer = this.singleQuestion.option_2.risk_value;
                }
                if (this.singleQuestion.option_3.answer !== undefined) {
                    this.singleQuestion.option_3.answer = this.singleQuestion.option_3.risk_value;
                }
                if (this.singleQuestion.option_4.answer !== undefined) {
                    this.singleQuestion.option_4.answer = this.singleQuestion.option_4.risk_value;
                }
            }
        },

        no_risk_confirm_auto: function () {
            let no_risk_confirm = document.getElementById('no_risk_confirm');
            let no_risk_check = 1;

            const q1 = $('[name="' + this.singleQuestion._id + '_q1"]:checked').val();
            const q2 = $('[name="' + this.singleQuestion._id + '_q2"]:checked').val();
            const q3 = $('[name="' + this.singleQuestion._id + '_q3"]:checked').val();
            const q4 = $('[name="' + this.singleQuestion._id + '_q4"]:checked').val();
            if (q1 !== undefined && parseInt(q1) > 0) {
                no_risk_check = 0;
            }
            if (q2 !== undefined && parseInt(q2) > 0) {
                no_risk_check = 0;
            }
            if (q3 !== undefined && parseInt(q3) > 0) {
                no_risk_check = 0;
            }
            if (q4 !== undefined && parseInt(q4) > 0) {
                no_risk_check = 0;
            }
            if (no_risk_confirm != null) {
                if (no_risk_check === 1) {
                    no_risk_confirm.checked = true;
                } else {
                    no_risk_confirm.checked = false;
                }
            }
        },

        downloadCertificate: function () {
            ApiService.GETPDF(ApiRoutes.UserEvaluationCertificate + '/' + this.risk_evaluation.evaluation._id, (response) => {
                const disposition = response.headers['content-disposition'];
                let filename = 'certificate.pdf';
                if (disposition && disposition.includes('filename=')) {
                    const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                    if (filenameMatch != null && filenameMatch[1]) {
                        filename = filenameMatch[1].replace(/['"]/g, ''); // remove quotes
                    }
                }

                const file = new Blob([response.data], { type: 'application/pdf' });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(file);
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        }
    },
    created() {
        this.evaluation_report();
    },
    mounted() {
        window.scrollTo(0, 0);
    },
}
</script>

<style scoped>
/* Main Layout */
.risk-evaluation-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Loading States */
.loading-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.loading-card {
    background: white;
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    max-width: 400px;
    width: 100%;
}

.loading-icon {
    margin-bottom: 1.5rem;
}

.loading-icon i {
    font-size: 3rem;
    color: #dc2626;
}

.loading-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.loading-subtitle {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.progress-container {
    background: #e5e7eb;
    border-radius: 50px;
    height: 8px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    background: linear-gradient(90deg, #dc2626, #ef4444);
    height: 100%;
    border-radius: 50px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Report Header */
.report-header {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.header-navigation {
    margin-bottom: 1.5rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.back-btn:hover {
    color: #dc2626;
    background: #f3f4f6;
    text-decoration: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
}

.project-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.project-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 0.025em;
}

.type-badge.type-specific {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.type-badge.type-non-tech {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.type-badge.type-fair {
    background: linear-gradient(135deg, #10b981, #059669);
}

.header-actions {
    flex-shrink: 0;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(5, 150, 105, 0.4);
    color: white;
}

/* Risk Legend */
.risk-legend {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    margin-top: 1.5rem;
}

.legend-title {
    font-size: 1rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 1rem;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
}

.risk-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.risk-badge.no-risk {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.risk-badge.low-risk {
    background: #10b981;
    color: white;
}

.risk-badge.limited-risk {
    background: #f59e0b;
    color: white;
}

.risk-badge.high-risk {
    background: #ef4444;
    color: white;
}

/* Risk Overview */
.risk-overview {
    margin-bottom: 2rem;
}

.overview-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.overview-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 2rem;
}

.overview-title i {
    color: #dc2626;
}

.risk-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.risk-stat {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.risk-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    line-height: 1;
}

.stat-value.no-risk { color: #374151; }
.stat-value.low-risk { color: #10b981; }
.stat-value.limited-risk { color: #f59e0b; }
.stat-value.high-risk { color: #ef4444; }

/* Completion Certificate */
.completion-section {
    margin-bottom: 2rem;
}

.certificate-card {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.certificate-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

.certificate-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.certificate-title {
    font-size: 1.75rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.certificate-message {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.certificate-btn {
    background: white;
    color: #059669;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.certificate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Analysis Sections */
.analysis-sections {
    margin-bottom: 2rem;
}

.section-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.single-section {
    display: flex;
    justify-content: center;
}

.section-item {
    width: 100%;
}

.section-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.section-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #dc2626;
}

.section-content {
    padding: 2rem;
    min-height: 150px;
}

/* Empty Sections */
.empty-section {
    text-align: center;
    padding: 2rem 1rem;
}

.empty-icon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.empty-message {
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.answer-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.answer-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

/* Question Grid */
.question-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: flex-start;
}

.question-grid.centered {
    justify-content: center;
}

.question-bubble {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.question-bubble.large {
    width: 75px;
    height: 75px;
}

.question-bubble:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.question-number {
    font-weight: 700;
    font-size: 0.875rem;
    color: white;
    position: relative;
    z-index: 1;
}

.question-bubble.large .question-number {
    font-size: 1rem;
}

/* Risk Level Colors */
.risk-no, .question-bubble.risk-no {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    color: #374151;
    border-color: #d1d5db;
}

.risk-low, .question-bubble.risk-low {
    background: linear-gradient(135deg, #10b981, #059669);
}

.risk-limited, .question-bubble.risk-limited {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.risk-high, .question-bubble.risk-high {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* Results Section */
.results-section {
    margin-bottom: 2rem;
}

.results-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

/* Cards */
.mitigation-card, .chart-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    height: 500px;
    display: flex;
    flex-direction: column;
}

.card-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    flex-shrink: 0;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-title i {
    color: #dc2626;
}

.card-content {
    padding: 2rem;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Mitigation Content */
.mitigation-content {
    overflow-y: auto;
    flex: 1;
    line-height: 1.6;
    color: #374151;
}

.mitigation-content h1, .mitigation-content h2, .mitigation-content h3,
.mitigation-content h4, .mitigation-content h5, .mitigation-content h6 {
    color: #1f2937;
    font-weight: 700;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.mitigation-content h1 { font-size: 1.5rem; }
.mitigation-content h2 { font-size: 1.375rem; }
.mitigation-content h3 { font-size: 1.25rem; }
.mitigation-content h4 { font-size: 1.125rem; }
.mitigation-content h5 { font-size: 1rem; }
.mitigation-content h6 { font-size: 0.875rem; }

.mitigation-content p {
    margin-bottom: 1rem;
}

.mitigation-content ul, .mitigation-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.mitigation-content li {
    margin-bottom: 0.5rem;
}

.mitigation-content strong {
    color: #1f2937;
    font-weight: 600;
}

/* Premium Notice */
.premium-notice {
    text-align: center;
    padding: 2rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.premium-icon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.premium-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.premium-description {
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.premium-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
    color: white;
    text-decoration: none;
}

/* Chart Container */
.chart-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Custom Radio Checkbox Styles */
.custom-radio-checkbox .form-check {
    margin-right: 1rem;
}

.form-check-input-custom {
    margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .legend-items {
        justify-content: center;
    }
    
    .risk-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .project-title {
        font-size: 1.5rem;
    }
    
    .section-grid {
        grid-template-columns: 1fr;
    }
    
    .risk-stats {
        grid-template-columns: 1fr;
    }
    
    .legend-items {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .question-grid {
        justify-content: center;
    }
    
    .mitigation-card, .chart-card {
        height: auto;
        min-height: 400px;
    }
    
    .card-content {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        text-align: center;
    }
    
    .download-btn {
        width: 100%;
        justify-content: center;
    }
    
    .type-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .question-bubble {
        width: 45px;
        height: 45px;
    }
    
    .question-bubble.large {
        width: 60px;
        height: 60px;
    }
}

/* Print Styles */
@media print {
    .risk-evaluation-page {
        background: white !important;
    }
    
    .back-btn, .download-btn, .premium-btn, .answer-btn, 
    .certificate-btn, .question-bubble {
        display: none !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .report-header, .overview-card, .section-card, 
    .mitigation-card, .chart-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #e2e8f0;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .chart-card {
        height: auto;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.report-content {
    animation: fadeIn 0.6s ease-out;
}

.section-card, .overview-card, .mitigation-card, .chart-card {
    animation: fadeIn 0.6s ease-out;
}
</style>