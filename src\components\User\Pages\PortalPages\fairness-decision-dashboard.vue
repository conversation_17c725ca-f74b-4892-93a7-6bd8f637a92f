<!-- src/components/User/Pages/PortalPages/fairness-decision-dashboard.vue -->
<template>
    <div class="fairness-dashboard">
        <!-- Page Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fa fa-balance-scale title-icon"></i>
                        Fair Decision Analysis
                    </h1>
                    <p class="page-subtitle">
                        Analyze AI systems for bias and ensure fair decision-making processes
                    </p>
                </div>
                <div class="header-actions">
                    <router-link :to="{ name: 'StartFairDecisionAnalysis' }" class="btn-primary">
                        <i class="fa fa-plus"></i>
                        New Analysis
                    </router-link>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
            <p class="loading-text">Loading your analyses...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="evaluations.length === 0" class="empty-state">
            <div class="empty-content">
                <div class="empty-illustration">
                    <div class="scale-icon">
                        <i class="fa fa-balance-scale"></i>
                    </div>
                    <div class="floating-elements">
                        <div class="element element-1"></div>
                        <div class="element element-2"></div>
                        <div class="element element-3"></div>
                    </div>
                </div>
                <h2 class="empty-title">No Fair Decision Analyses Yet</h2>
                <p class="empty-description">
                    Start your first fairness analysis to identify potential bias and ensure 
                    equitable decision-making in your AI systems.
                </p>
                <div class="empty-actions">
                    <router-link :to="{ name: 'StartFairDecisionAnalysis' }" class="btn-primary large">
                        <i class="fa fa-balance-scale"></i>
                        Start Your First Fairness Analysis
                    </router-link>
                    <a href="https://raidot.ai" target="_blank" class="btn-secondary">
                        <i class="fa fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>
        </div>

        <!-- Analyses Grid -->
        <div v-else class="analyses-section">
            <div class="section-header">
                <h2 class="section-title">Your Fairness Analyses</h2>
                <div class="analysis-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ evaluations.length }}</span>
                        <span class="stat-label">Total Analyses</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ completedAnalyses }}</span>
                        <span class="stat-label">Completed</span>
                    </div>
                </div>
            </div>

            <div class="analyses-grid">
                <!-- New Analysis Card -->
                <div class="analysis-card new-analysis">
                    <router-link :to="{ name: 'StartFairDecisionAnalysis' }" class="card-link">
                        <div class="card-content">
                            <div class="new-analysis-icon">
                                <i class="fa fa-plus"></i>
                            </div>
                            <h3 class="card-title">New Fairness Analysis</h3>
                            <p class="card-description">
                                Start a comprehensive bias assessment for your AI system
                            </p>
                        </div>
                        <div class="card-footer">
                            <span class="action-text">Create Analysis</span>
                            <i class="fa fa-arrow-right"></i>
                        </div>
                    </router-link>
                </div>

                <!-- Analysis Cards -->
                <div 
                    v-for="evaluation in evaluations" 
                    :key="evaluation._id"
                    class="analysis-card"
                    :class="getAnalysisCardClass(evaluation)"
                >
                    <div class="card-header">
                        <div class="analysis-type">
                            <div class="type-icon" :class="getTypeIconClass(evaluation.category)">
                                <i class="fa fa-balance-scale"></i>
                            </div>
                            <span class="type-label">{{ getAnalysisType(evaluation.category) }}</span>
                        </div>
                        <div class="card-actions">
                            <button 
                                @click="deleteAnalysis(evaluation)" 
                                class="delete-btn"
                                :title="`Delete ${evaluation.project.name}`"
                            >
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="card-content">
                        <h3 class="card-title">{{ evaluation.project.name }}</h3>
                        <p class="card-description" v-if="evaluation.project.desc">
                            {{ evaluation.project.desc }}
                        </p>
                        <div class="analysis-meta">
                            <div class="meta-item">
                                <i class="fa fa-calendar"></i>
                                <span>{{ formatDate(evaluation.created_at) || 'No date available' }}</span>
                            </div>
                            <div class="meta-item" v-if="evaluation.project.website">
                                <i class="fa fa-globe"></i>
                                <span>{{ getDomainFromUrl(evaluation.project.website) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <router-link 
                        :to="{ name: 'FairnessReport', params: { evaluation_id: evaluation._id } }"                            class="view-report-btn"
                        >
                            <span>View Analysis</span>
                            <i class="fa fa-external-link"></i>
                        </router-link>
                    </div>

                    <!-- Fairness Level Indicator -->
                    <div class="fairness-indicator" v-if="evaluation.fairness_level">
                        <div 
                            class="fairness-badge" 
                            :class="`fairness-${evaluation.fairness_level.toLowerCase()}`"
                        >
                            {{ evaluation.fairness_level }} Fairness
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

// MINIMAL CHANGES - Only replace the script section in fairness-decision-dashboard.vue
// Keep the template exactly as it was, only change the script section

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import Swal from "sweetalert2";
import { useFairnessAnalysisActions } from '@/composables/fairnessAnalysis/useFairnessAnalysisActions';

export default {
    name: 'FairnessDecisionDashboard',
    data() {
        return {
            loading: true,
            evaluations: [],
            fairnessAnalysisActions: null
        }
    },
    computed: {
        completedAnalyses() {
            return this.evaluations.filter(evaluation => {
                // Check if evaluation has a report with riskPercentage and No risk = 100%
                return evaluation.report && 
                    evaluation.report.riskPercentage && 
                    evaluation.report.riskPercentage.No === 100;
            }).length;
        }
    },
    async created() {
        // Initialize fairness analysis actions
        this.fairnessAnalysisActions = useFairnessAnalysisActions();
        
        // Set analysis type to fairness
        localStorage.setItem('risk_analysis', 'fair');
        
        // Load analyses
        this.loadAnalyses();
    },
    mounted() {
        window.scrollTo(0, 0);
    },
    methods: {
        loadAnalyses() {
            this.loading = true;
            ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
                this.loading = false;
                if (res.status === 200) {
                    // Filter for fairness evaluations only
                    this.evaluations = res.data.filter(e => ['fd', 'eta-fd'].includes(e.category));
                } else {
                    console.error('Failed to load evaluations:', res);
                }
            });
        },

        deleteAnalysis(evaluationId) {
            Swal.fire({
                title: 'Delete Analysis?',
                text: "You won't be able to revert this!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#0d9f56',
                cancelButtonColor: '#777777',
                confirmButtonText: 'Yes, delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    const data = { evaluation_id: evaluationId };
                    ApiService.POST(ApiRoutes.UserEvaluationDelete, data, (res) => {
                        if (res.status === 200) {
                            this.loadAnalyses();
                            Swal.fire({
                                title: 'Deleted!',
                                text: 'Analysis has been deleted successfully.',
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                        }
                    });
                }
            });
        },

        getAnalysisType(category) {
            const types = {
                'fd': 'General Fairness',
                'eta-fd': 'Industry Specific'
            };
            return types[category] || 'Fair Decision Analysis';
        },

        getTypeIconClass(category) {
            const classes = {
                'fd': 'type-general',
                'eta-fd': 'type-industry'
            };
            return classes[category] || 'type-general';
        },

        getAnalysisCardClass(evaluation) {
            return `analysis-${evaluation.category}`;
        },

        formatDate(dateString) {
            if (!dateString) return 'No date available';
            
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid date';
            
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },

        getDomainFromUrl(url) {
            try {
                const domain = new URL(url).hostname;
                return domain.replace('www.', '');
            } catch {
                return url;
            }
        }
    }
}
</script>

<style scoped>
.fairness-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

.btn-primary.large {
    padding: 1.125rem 2.5rem;
    font-size: 1.125rem;
}

.btn-secondary {
    background: white;
    color: #7c3aed;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-secondary:hover {
    background: #f3f4f6;
    border-color: #7c3aed;
    transform: translateY(-2px);
    color: #5b21b6;
    text-decoration: none;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
}

.loading-spinner {
    font-size: 3rem;
    color: #7c3aed;
    margin-bottom: 1rem;
}

.loading-text {
    color: #6b7280;
    font-size: 1.125rem;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
}

.empty-illustration {
    position: relative;
    margin-bottom: 2rem;
    display: inline-block;
}

.scale-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #ede9fe, #ddd6fe);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #7c3aed;
    position: relative;
    z-index: 1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    opacity: 0.1;
    animation: float 3s ease-in-out infinite;
}

.element-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation-delay: -1s;
}

.element-2 {
    width: 16px;
    height: 16px;
    top: 60%;
    right: 20%;
    animation-delay: -2s;
}

.element-3 {
    width: 12px;
    height: 12px;
    bottom: 20%;
    left: 30%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.empty-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto 2.5rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Analyses Section */
.analyses-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.analysis-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #7c3aed;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.analyses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Analysis Cards */
.analysis-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analysis-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #7c3aed;
}

.analysis-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.analysis-card:hover::before {
    left: 100%;
}

/* New Analysis Card */
.new-analysis {
    border: 2px dashed #d1d5db;
    background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
}

.new-analysis:hover {
    border-color: #7c3aed;
    background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
}

.card-link {
    display: block;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    height: 100%;
    position: relative;
    z-index: 2;
}

.new-analysis-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

/* Regular Analysis Cards */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.analysis-type {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.type-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.type-general {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
}

.type-industry {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
}

.type-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.delete-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(124, 58, 237, 0.1);
    color: #7c3aed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-btn:hover {
    background: #7c3aed;
    color: white;
    transform: scale(1.1);
}

.card-content {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.card-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.analysis-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.meta-item i {
    width: 16px;
    color: #9ca3af;
}

.card-footer {
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.view-report-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.875rem 1.25rem;
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.view-report-btn:hover {
    background: linear-gradient(135deg, #5b21b6, #4c1d95);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
    color: white;
    text-decoration: none;
}

.view-report-btn i {
    transition: transform 0.3s ease;
}

.view-report-btn:hover i {
    transform: translateX(4px);
}

/* Fairness Indicator */
.fairness-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 3;
}

.fairness-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
}

.fairness-high {
    background: linear-gradient(135deg, #10b981, #059669);
}

.fairness-medium {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.fairness-low {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fairness-poor {
    background: linear-gradient(135deg, #7c2d12, #991b1b);
}

/* Analysis Type Variants */
.analysis-fd {
    border-left: 4px solid #0ea5e9;
}

.analysis-eta-fd {
    border-left: 4px solid #7c3aed;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .analysis-stats {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .analyses-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .analysis-stats {
        justify-content: center;
        width: 100%;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .card-content {
        padding: 1.25rem;
    }
    
    .card-footer {
        padding: 0 1.25rem 1.25rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-link,
    .card-content {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem 1rem 1rem;
    }
    
    .card-footer {
        padding: 0 1rem 1rem;
    }
    
    .analysis-meta {
        gap: 0.375rem;
    }
    
    .meta-item {
        font-size: 0.8125rem;
    }
}

/* Enhanced animations and micro-interactions */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.analysis-card {
    animation: slideInUp 0.6s ease-out;
}

.analysis-card:nth-child(1) { animation-delay: 0.1s; }
.analysis-card:nth-child(2) { animation-delay: 0.2s; }
.analysis-card:nth-child(3) { animation-delay: 0.3s; }
.analysis-card:nth-child(4) { animation-delay: 0.4s; }
.analysis-card:nth-child(5) { animation-delay: 0.5s; }
.analysis-card:nth-child(6) { animation-delay: 0.6s; }

/* Focus states for accessibility */
.card-link:focus,
.view-report-btn:focus,
.delete-btn:focus {
    outline: 2px solid #7c3aed;
    outline-offset: 2px;
}

/* Loading animation for cards */
.analysis-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.analysis-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #7c3aed;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
</style>