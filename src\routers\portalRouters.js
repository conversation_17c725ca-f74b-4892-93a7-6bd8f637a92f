import Layout from "@/components/User/Pages/layout/layout.vue";

import Dashboard from "@/components/User/Pages/PortalPages/dashboard.vue";
import Profile from "@/components/User/Pages/PortalPages/profile.vue";
import ProfileUpdate from "@/components/User/Pages/PortalPages/profile-update.vue";
import ProfilePasswordUpdate from "@/components/User/Pages/PortalPages/profile-password-update.vue";
import RiskEvaluation from "@/components/User/Pages/PortalPages/risk-evaluation-dashboard.vue";
import FairDecisionAnalysis from "@/components/User/Pages/PortalPages/fairness-decision-dashboard.vue";
import AwarenessEvaluation from "@/components/User/Pages/PortalPages/awareness-evaluation-dashboard.vue";

// Risk Evaluation Pages (only risk-related files)
import StartRiskEvaluation from "@/components/User/Pages/RiskEvaluationPages/start-risk-evaluation.vue";
import EvaluationReport from "@/components/User/Pages/RiskEvaluationPages/risk-evaluation-report.vue";
import AskAiSystems from "@/components/User/Pages/RiskEvaluationPages/ask-ai-systems.vue";
import EtEta from "@/components/User/Pages/RiskEvaluationPages/et-eta.vue";
import EtNt from "@/components/User/Pages/RiskEvaluationPages/et-nt.vue";
import PlanningAiSystems from "@/components/User/Pages/RiskEvaluationPages/planning-ai-systems.vue";
import TrainingAiSystems from "@/components/User/Pages/RiskEvaluationPages/training-ai-systems.vue";
import EtQuestions from "@/components/User/Pages/RiskEvaluationPages/et-questions.vue";
import NtQuestions from "@/components/User/Pages/RiskEvaluationPages/nt-questions.vue";
import SafetyRisksDomains from "@/components/User/Pages/RiskEvaluationPages/safety-risks-domains.vue";
import BenefitAiSystems from "@/components/User/Pages/RiskEvaluationPages/benefit-ai-systems.vue";
import AlmostDone from "@/components/User/Pages/RiskEvaluationPages/almost_done.vue";
import EtQuestionsDomain from "@/components/User/Pages/RiskEvaluationPages/et-questions-domain.vue";

// Fairness Analysis Pages (moved to dedicated folder)
import StartFairDecisionAnalysis from "@/components/User/Pages/FairDecisionAnalysisPages/start-fair-decision-analysis.vue";
import FdQuestions from "@/components/User/Pages/FairDecisionAnalysisPages/fd-questions.vue";
import EtaFdQuestions from "@/components/User/Pages/FairDecisionAnalysisPages/eta-fd-questions.vue";
import FairnessDomains from "@/components/User/Pages/FairDecisionAnalysisPages/fairness-domains.vue";
import FairnessChoice from "@/components/User/Pages/FairDecisionAnalysisPages/fairness-choice.vue";
import FairnessAlmostDone from "@/components/User/Pages/FairDecisionAnalysisPages/fairness-almost-done.vue";
import FairnessReport from "@/components/User/Pages/FairDecisionAnalysisPages/fairness-report.vue";

import UserConsultancyDashboard from "@/components/User/Pages/ConsultancyPages/ConsultancyDashboard.vue";

// Awareness Pages
import CourseList from "@/components/User/Pages/AwarenessPages/course-list.vue";
import AwarenessCoursePreview from "@/components/User/Pages/AwarenessPages/Course/course-preview.vue";
import LessonPreview from "@/components/User/Pages/AwarenessPages/Course/Lessons/lesson-preview.vue";

const PORTAL_ROOT_URL = "/portal";
const AWARENESS_ROOT_URL = "/portal/awareness-evaluation";

export default [
    {
        path: PORTAL_ROOT_URL, name: 'Layout', component: Layout,
        children: [
            { path: PORTAL_ROOT_URL + '/', redirect: { name: 'Dashboard' }, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/dashboard', name: 'Dashboard', component: Dashboard, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/profile', name: 'Profile', component: Profile, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/profile-update', name: 'ProfileUpdate', component: ProfileUpdate, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/profile-password-update', name: 'ProfilePasswordUpdate', component: ProfilePasswordUpdate, meta: { requiresAuth: true } },
            
            // Risk Evaluation Routes
            { path: PORTAL_ROOT_URL + '/risk-evaluation', name: 'RiskEvaluation', component: RiskEvaluation, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/ask-ai-systems', name: 'AskAiSystems', component: AskAiSystems, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/et-nt', name: 'EtNt', component: EtNt, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/planning-for-ai', name: 'PlanningAiSystems', component: PlanningAiSystems, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/training-of-ai', name: 'TrainingAiSystems', component: TrainingAiSystems, meta: { requiresAuth: true } }, 
            { path: PORTAL_ROOT_URL + '/risk-evaluation/et-questions', name: 'EtQuestions', component: EtQuestions, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/nt-questions', name: 'NtQuestions', component: NtQuestions, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/benefit-of-ai', name: 'BenefitAiSystems', component: BenefitAiSystems, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/risk-evaluation/eta-questions/:domain', name: 'EtQuestionsDomain', component: EtQuestionsDomain, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/evaluation/start', name: 'StartRiskEvaluation', component: StartRiskEvaluation, meta: { requiresAuth: true, requiresEvaluationValidity: true, } },
            { path: PORTAL_ROOT_URL + '/evaluation/et-eta', name: 'EtEta', component: EtEta, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/evaluation/safety-risks-domains', name: 'SafetyRisksDomains', component: SafetyRisksDomains, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/evaluation/almost-done', name: 'AlmostDone', component: AlmostDone, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/evaluation/report/:evaluation_id', name: 'EvaluationReport', component: EvaluationReport, meta: { requiresAuth: true } },
            
            // Fairness Analysis Routes (using new folder structure)
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis', name: 'FairDecisionAnalysis', component: FairDecisionAnalysis, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/start', name: 'StartFairDecisionAnalysis', component: StartFairDecisionAnalysis, meta: { requiresAuth: true, requiresEvaluationValidity: true, } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/domains', name: 'FairnessDomains', component: FairnessDomains, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/fd-questions', name: 'FdQuestions', component: FdQuestions, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/eta-fd-questions/:domain', name: 'EtaFdQuestions', component: EtaFdQuestions, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/choice', name: 'FairnessChoice', component: FairnessChoice, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/almost-done', name: 'FairnessAlmostDone', component: FairnessAlmostDone, meta: { requiresAuth: true } },
            { path: PORTAL_ROOT_URL + '/fair-decision-analysis/report/:evaluation_id', name: 'FairnessReport', component: FairnessReport, meta: { requiresAuth: true } },

            // Awareness Evaluation Routes
            { path: PORTAL_ROOT_URL + '/awareness-evaluation', name: 'AwarenessEvaluation', component: AwarenessEvaluation, meta: { requiresAuth: true } },
            { path: AWARENESS_ROOT_URL + '/course/all', name: 'CourseList', component: CourseList, meta: { requiresAuth: true } },
            {
                path: AWARENESS_ROOT_URL + '/course/:course_id', name: 'AwarenessCoursePreview', component: AwarenessCoursePreview,
                children: [
                    {path: 'topic/:topic_id/preview/:lesson_id', name: 'LessonPreview', component: LessonPreview,}
                ]
            },
            
            { 
                path: PORTAL_ROOT_URL + '/consultancy', 
                name: 'UserConsultancy', 
                component: UserConsultancyDashboard,
                meta: { requiresAuth: true }
            },

        ]
    },
]