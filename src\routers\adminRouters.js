import AdminLogin from "@/components/Admin/Pages/AuthPages/adminLogin.vue";
import AdminLayout from "@/components/Admin/Pages/Layout/adminLayout.vue";
import AdminDashboard from "@/components/Admin/Pages/DashboardPages/adminDashboard.vue";
import AdminProfile from "@/components/Admin/Pages/ProfilePages/adminProfile.vue";
import AccountSettings from "@/components/Admin/Pages/ProfilePages/subComponents/accountSettings.vue";
import ChangePassword from "@/components/Admin/Pages/ProfilePages/subComponents/changePassword.vue";
import Details from "@/components/Admin/Pages/ProfilePages/subComponents/details.vue";
import EditProfile from "@/components/Admin/Pages/ProfilePages/subComponents/editProfile.vue";
import NotificationSettings from "@/components/Admin/Pages/ProfilePages/subComponents/notificationSettings.vue";
import RiskFactors from "@/components/Admin/Pages/EvaluationPages/risk-factors.vue";
import Sectors from "@/components/Admin/Pages/EvaluationPages/sectors.vue";
import FdSectors from "@/components/Admin/Pages/EvaluationPages/fd-sectors.vue";
import Users from "@/components/Admin/Pages/UsersManagementPages/users.vue";
import UserCreate from "@/components/Admin/Pages/UsersManagementPages/user-create.vue";
import UserEdit from "@/components/Admin/Pages/UsersManagementPages/user-edit.vue";
import cgptQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/cgptQuestionnaires.vue";
import ntQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/ntQuestionnaires.vue";
import etQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/etQuestionnaires.vue";
import etaQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/etaQuestionnaires.vue";
import fdQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/fdQuestionnaires.vue";
import etaFdQuestionnaires from "@/components/Admin/Pages/QuestionnairesPages/etaFdQuestionnaires.vue";
import Workshops from "@/components/Admin/Pages/WorkshopsManagementPages/workshops.vue";
import WorkshopCreate from "@/components/Admin/Pages/WorkshopsManagementPages/workshop-create.vue";
import WorkshopEdit from "@/components/Admin/Pages/WorkshopsManagementPages/workshop-edit.vue";
import WorkshopPreview from "@/components/Admin/Pages/WorkshopsManagementPages/workshop-preview.vue";
import EvaluationCertification from "@/components/Admin/Pages/CertificationManagementPages/evaluation-certification.vue";
import EvaluationCertificationSettings from "@/components/Admin/Pages/CertificationManagementPages/evaluation-certification-settings.vue";
import ParticipationCertification from "@/components/Admin/Pages/CertificationManagementPages/participation-certification.vue";
import ParticipantCertificationSettings from "@/components/Admin/Pages/CertificationManagementPages/participant-certification-settings.vue";
import AwarenessCertification from "@/components/Admin/Pages/CertificationManagementPages/awareness-certification.vue";
import AwarenessCertificationSettings from "@/components/Admin/Pages/CertificationManagementPages/awareness-certification-settings.vue";
import Awareness from "@/components/Admin/Pages/AwarenessManagementPages/Awareness.vue";
import AwarenessCreate from "@/components/Admin/Pages/AwarenessManagementPages/AwarenessCreate.vue";
import AwarenessEdit from "@/components/Admin/Pages/AwarenessManagementPages/AwarenessEdit.vue";
import AwarenessPreview from "@/components/Admin/Pages/AwarenessManagementPages/AwarenessPreview.vue";
import LessonCreate from "@/components/Admin/Pages/AwarenessManagementPages/lessons/LessonCreate.vue";
import LessonEdit from "@/components/Admin/Pages/AwarenessManagementPages/lessons/LessonEdit.vue";
import AdminLessonPreview from "@/components/Admin/Pages/AwarenessManagementPages/lessons/LessonPreview.vue";

import PricingCreate from "@/components/Admin/Pages/PricingPages/PricingCreate.vue";
import PricingList from "@/components/Admin/Pages/PricingPages/PricingList.vue";
import AdminPricingEdit from "@/components/Admin/Pages/PricingPages/PricingEdit.vue";

import AdminConsultancySlots from "@/components/Admin/Pages/ConsultancyPages/ConsultancySlots.vue";
import AdminConsultancyBookings from "@/components/Admin/Pages/ConsultancyPages/ConsultancyBookings.vue";
import AdminConsultancyDashboard from "@/components/Admin/Pages/ConsultancyPages/ConsultancyDashboard.vue";

const ADMIN_ROOT_URL = "/admin";

export default [
    { path: ADMIN_ROOT_URL + '/auth/login', name: 'AdminLogin', component: AdminLogin },
    { 
        path: ADMIN_ROOT_URL, name: 'AdminLayout', component: AdminLayout, meta: { requiresAuth: true },
        children: [
            { path: ADMIN_ROOT_URL + '/', redirect: { name: 'AdminDashboard' } },
            { path: ADMIN_ROOT_URL + '/dashboard', name: 'AdminDashboard', component: AdminDashboard, },
            {
                path: ADMIN_ROOT_URL + '/profile', name: 'AdminProfileLayout', component: AdminProfile,
                children: [
                    { path: ADMIN_ROOT_URL + '/', name: 'AdminProfile', component: Details },
                    { path: ADMIN_ROOT_URL + '/profile/editProfile', name: 'AdminEditProfile', component: EditProfile },
                    { path: ADMIN_ROOT_URL + '/profile/changePassword', name: 'AdminChangePassword', component: ChangePassword },
                    { path: ADMIN_ROOT_URL + '/profile/notificationSettings', name: 'AdminNotificationSettings', component: NotificationSettings },
                    { path: ADMIN_ROOT_URL + '/profile/accountSettings', name: 'AdminAccountSettings', component: AccountSettings },
                ]
            },
            { path: ADMIN_ROOT_URL + '/evaluation/risk-factors', name: 'RiskFactors', component: RiskFactors },
            { path: ADMIN_ROOT_URL + '/evaluation/sectors', name: 'Sectors', component: Sectors },
            { path: ADMIN_ROOT_URL + '/fair-decision/sectors', name: 'FdSectors', component: FdSectors },

            { path: ADMIN_ROOT_URL + '/users', name: 'Users', component: Users },
            { path: ADMIN_ROOT_URL + '/user/create', name: 'UserCreate', component: UserCreate },
            { path: ADMIN_ROOT_URL + '/user/edit/:user_id', name: 'UserEdit', component: UserEdit },
            
            { path: ADMIN_ROOT_URL + '/evaluation/non-tech/questionnaires', name: 'ntQuestionnaires', component: ntQuestionnaires },
            { path: ADMIN_ROOT_URL + '/evaluation/chat-gpt/questionnaires', name: 'cgptQuestionnaires', component: cgptQuestionnaires },
            { path: ADMIN_ROOT_URL + '/evaluation/tech/general/Questionnaires', name: 'etQuestionnaires', component: etQuestionnaires },
            { path: ADMIN_ROOT_URL + '/evaluation/tech/application/Questionnaires', name: 'etaQuestionnaires', component: etaQuestionnaires },
            { path: ADMIN_ROOT_URL + '/evaluation/fair-decision-general/questionnaires', name: 'fdQuestionnaires', component: fdQuestionnaires },
            { path: ADMIN_ROOT_URL + '/evaluation/fair-decision-domain/questionnaires', name: 'etaFdQuestionnaires', component: etaFdQuestionnaires },
            
            { path: ADMIN_ROOT_URL + '/workshops', name: 'Workshops', component: Workshops },
            { path: ADMIN_ROOT_URL + '/workshop/create', name: 'WorkshopCreate', component: WorkshopCreate },
            { path: ADMIN_ROOT_URL + '/workshop/edit/:workshop_id', name: 'WorkshopEdit', component: WorkshopEdit },
            { path: ADMIN_ROOT_URL + '/workshop/preview/:workshop_id', name: 'WorkshopPreview', component: WorkshopPreview },

            { path: ADMIN_ROOT_URL + '/certification/evaluation', name: 'EvaluationCertification', component: EvaluationCertification },
            { path: ADMIN_ROOT_URL + '/certification/evaluation/settings', name: 'EvaluationCertificationSettings', component: EvaluationCertificationSettings },
            { path: ADMIN_ROOT_URL + '/certification/participation', name: 'ParticipationCertification', component: ParticipationCertification },
            { path: ADMIN_ROOT_URL + '/certification/participation/settings', name: 'ParticipantCertificationSettings', component: ParticipantCertificationSettings },
            { path: ADMIN_ROOT_URL + '/certification/awareness', name: 'AwarenessCertification', component: AwarenessCertification },
            { path: ADMIN_ROOT_URL + '/certification/awareness/settings', name: 'AwarenessCertificationSettings', component: AwarenessCertificationSettings },
            
            { path: ADMIN_ROOT_URL + '/evaluation/awareness', name: 'Awareness', component: Awareness },
            { path: ADMIN_ROOT_URL + '/evaluation/awareness/create', name: 'AwarenessCreate', component: AwarenessCreate },
            { path: ADMIN_ROOT_URL + '/evaluation/awareness/edit/:course_id', name: 'AwarenessEdit', component: AwarenessEdit },
            {
                path: ADMIN_ROOT_URL + '/evaluation/awareness/preview/:course_id', name: 'AwarenessPreview', component: AwarenessPreview,
                children: [
                    {path: ADMIN_ROOT_URL + '/evaluation/awareness/preview/:course_id/topic/:topic_id/create', name: 'LessonCreate', component: LessonCreate,},
                    {path: ADMIN_ROOT_URL + '/evaluation/awareness/preview/:course_id/topic/:topic_id/edit/:lesson_id', name: 'LessonEdit', component: LessonEdit,},
                    {path: ADMIN_ROOT_URL + '/evaluation/awareness/preview/:course_id/topic/:topic_id/preview/:lesson_id', name: 'AdminLessonPreview', component: AdminLessonPreview,}
                ]
            },  
            
            { path: ADMIN_ROOT_URL + '/pricing/list', name: 'AdminPricingList', component: PricingList },
            { path: ADMIN_ROOT_URL + '/pricing/create', name: 'AdminPricingCreate', component: PricingCreate },
            { path: ADMIN_ROOT_URL + '/pricing/edit/:package_id', name: 'AdminPricingEdit', component: AdminPricingEdit },

            { path: ADMIN_ROOT_URL + '/consultancy/dashboard', name: 'AdminConsultancyDashboard', component: AdminConsultancyDashboard },
            { path: ADMIN_ROOT_URL + '/consultancy/slots', name: 'AdminConsultancySlots', component: AdminConsultancySlots },
            { path: ADMIN_ROOT_URL + '/consultancy/bookings', name: 'AdminConsultancyBookings', component: AdminConsultancyBookings },

        ] 
    },
]