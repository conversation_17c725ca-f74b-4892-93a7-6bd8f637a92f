// Fix for src/services/ConsultancyService.js - Complete version

import ApiService from './ApiService';
import ConsultancyApiRoutes from '../ApiRoutes/consultancyApiRoutes';

// Helper function for consistent response handling
const handleResponse = (res, resolve, reject, errorMessage) => {
    if (res.status === 200) {
        resolve(res);
    } else {
        const error = res.error || res.message || errorMessage;
        reject(new Error(error));
    }
};

const ConsultancyService = {
    // Admin Services
    admin: {
        // Slot Management
        getAllSlots(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetAllSlots, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch slots")
                );
            });
        },

        createSlot(slotData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.CreateSlot, slotData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to create slot")
                );
            });
        },

        updateSlot(slotData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateSlot, slotData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update slot")
                );
            });
        },

        deleteSlot(slotId) {
            return new Promise((resolve, reject) => {
                ApiService.DELETE(ConsultancyApiRoutes.Admin.DeleteSlot(slotId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to delete slot")
                );
            });
        },

        getSlot(slotId) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetSlot(slotId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch slot")
                );
            });
        },

        // Booking Management
        getAllBookings(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetAllBookings, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch bookings")
                );
            });
        },

        getBooking(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetBooking(bookingId), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch booking")
                );
            });
        },

        updateBooking(bookingData) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateBooking, bookingData, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update booking")
                );
            });
        },

        confirmBooking(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.ConfirmBooking, { booking_id: bookingId }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to confirm booking")
                );
            });
        },

        cancelBooking(bookingId, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.CancelBooking, { 
                    booking_id: bookingId, 
                    cancellation_reason: reason 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to cancel booking")
                );
            });
        },

        // Meeting Link Management
        updateMeetingLink(bookingId, meetingLink) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateMeetingLink, { 
                    booking_id: bookingId, 
                    meeting_link: meetingLink 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update meeting link")
                );
            });
        },

        sendMeetingLink(bookingId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.SendMeetingLink, { 
                    booking_id: bookingId 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to send meeting link")
                );
            });
        },

        // Statistics
        getStats() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetStats, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch statistics")
                );
            });
        },

        // Reports
        getBookingReport(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetBookingReport, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch booking report")
                );
            });
        },

        getUsageReport(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetUsageReport, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch usage report")
                );
            });
        },

        // User Management
        getUserUsage(userId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.GetUserUsage, { user_id: userId }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch user usage")
                );
            });
        },

        updateUserUsage(userId, remainingMinutes, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.UpdateUserUsage, { 
                    user_id: userId, 
                    remaining_minutes: remainingMinutes,
                    reason: reason
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to update user usage")
                );
            });
        },

        initializeUserConsultancy(userId, packageId) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.Admin.InitializeUserConsultancy, { 
                    user_id: userId, 
                    package_id: packageId 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to initialize user consultancy")
                );
            });
        },

        // Location services
        getCountries() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetCountries, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch countries")
                );
            });
        },

        getCities(country) {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.Admin.GetCities(country), (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch cities")
                );
            });
        }
    },

    // User Services
    user: {
        // Get available slots for booking
        getAvailableSlots(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.GetAvailableSlots, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch available slots")
                );
            });
        },

        // Book a session
        bookSession(slotId, notes = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.BookSession, { 
                    slot_id: slotId, 
                    notes: notes 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to book session")
                );
            });
        },

        // Get user's bookings
        getMyBookings(filters = {}) {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.GetMyBookings, filters, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch bookings")
                );
            });
        },

        // Cancel booking
        cancelBooking(bookingId, reason = '') {
            return new Promise((resolve, reject) => {
                ApiService.POST(ConsultancyApiRoutes.User.CancelBooking, { 
                    booking_id: bookingId, 
                    cancellation_reason: reason 
                }, (res) =>
                    handleResponse(res, resolve, reject, "Failed to cancel booking")
                );
            });
        },

        // Get consultancy usage
        getMyUsage() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.User.GetMyUsage, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch usage data")
                );
            });
        },

        // Get upcoming sessions
        getUpcomingSessions() {
            return new Promise((resolve, reject) => {
                ApiService.GET(ConsultancyApiRoutes.User.GetUpcomingSessions, (res) =>
                    handleResponse(res, resolve, reject, "Failed to fetch upcoming sessions")
                );
            });
        },

        // Validate booking before submission
        validateBooking(slotId) {
            return new Promise((resolve, reject) => {
                // Check user's consultancy usage first
                this.getMyUsage()
                    .then(usageResponse => {
                        const usage = usageResponse.data;
                        if (!usage) {
                            reject(new Error('No consultancy access. Please upgrade your package.'));
                            return;
                        }
                        
                        if (usage.remaining_minutes <= 0) {
                            reject(new Error('No consultancy hours remaining. Please upgrade your package.'));
                            return;
                        }
                        
                        // Additional validation can be added here
                        resolve({
                            valid: true,
                            remaining_minutes: usage.remaining_minutes,
                            remaining_hours: Math.round(usage.remaining_minutes / 60 * 100) / 100
                        });
                    })
                    .catch(error => {
                        reject(error);
                    });
            });
        }
    },

    // Utility methods
    formatDuration(minutes) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        
        if (hours === 0) {
            return `${mins} minutes`;
        } else if (mins === 0) {
            return `${hours} hour${hours > 1 ? 's' : ''}`;
        } else {
            return `${hours}h ${mins}m`;
        }
    },

    formatDateTime(dateString, timeString) {
        const date = new Date(dateString + ' ' + timeString);
        return date.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    getStatusBadgeClass(status) {
        const statusClasses = {
            'pending': 'badge-warning',
            'confirmed': 'badge-success',
            'cancelled': 'badge-danger',
            'completed': 'badge-info'
        };
        return statusClasses[status] || 'badge-secondary';
    },

    canCancelBooking(booking) {
        if (booking.status !== 'confirmed' && booking.status !== 'pending') {
            return false;
        }
        
        // Check if booking is more than 24 hours away
        const slotDateTime = new Date(booking.slot.date + ' ' + booking.slot.start_time);
        const now = new Date();
        const hoursDifference = (slotDateTime - now) / (1000 * 60 * 60);
        
        return hoursDifference > 24;
    }
};

export default ConsultancyService;