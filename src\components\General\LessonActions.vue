<template>
  <div class="lesson-actions">
    <!-- Mark as Complete Action -->
    <div v-if="!lesson.completed && lesson.is_quiz === '0'" class="action-group">
      <button @click="handleMarkComplete" class="complete-lesson-btn" :disabled="loading">
        <i class="fa fa-check"></i>
        <span v-if="!loading">Mark as Complete</span>
        <span v-else>Marking Complete...</span>
      </button>
    </div>
    
    <!-- Completion Message -->
    <div v-else-if="lesson.completed && lesson.is_quiz === '0'" class="completion-message">
      <div class="completion-card">
        <i class="fa fa-trophy completion-icon"></i>
        <h3>Lesson Completed!</h3>
        <p>Great job! You've successfully completed this lesson.</p>
      </div>
    </div>
    
    <!-- Quiz Actions -->
    <div v-if="lesson.is_quiz === '1'" class="quiz-actions">
      <!-- Submit Quiz -->
      <button 
        v-if="!quizResult && showSubmitButton"
        @click="handleSubmitQuiz" 
        class="submit-quiz-btn"
        :disabled="loading || !allQuestionsAnswered"
      >
        <i class="fa fa-paper-plane"></i>
        <span v-if="!loading">Submit Quiz</span>
        <span v-else>Submitting...</span>
      </button>
      
      <!-- Retake Quiz -->
      <button 
        v-if="quizResult && lesson.is_final_exam === '0'"
        @click="handleRetakeQuiz" 
        class="retake-btn"
        :disabled="loading"
      >
        <i class="fa fa-refresh"></i>
        <span>Retake Quiz</span>
      </button>
      
      <!-- Certificate Download -->
      <button 
        v-if="quizResult && showCertificateButton"
        @click="handleDownloadCertificate" 
        class="certificate-btn"
        :disabled="loading"
      >
        <i class="fa fa-download"></i>
        <span>Download Certificate</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';

const props = defineProps({
  lesson: {
    type: Object,
    required: true
  },
  questions: {
    type: Array,
    default: () => []
  },
  quizResult: {
    type: Object,
    default: null
  },
  courseId: {
    type: String,
    required: true
  },
  topicId: {
    type: String,
    required: true
  },
  lessonId: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  showCertificateButton: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['mark-complete', 'submit-quiz', 'retake-quiz', 'download-certificate']);

const {
  allQuestionsAnswered,
  confirmMarkAsComplete,
  showLessonCompleted,
  showError,
  confirmSubmitQuiz,
  showIncompleteQuiz,
  showQuizSubmitted,
  confirmRetakeQuiz,
  showCertificateDownloaded,
  completeLesson,
  submitQuiz,
  downloadCourseCertificate
} = useAwarenessActions();

const allAnswered = computed(() => allQuestionsAnswered(props.questions));

const showSubmitButton = computed(() => {
  return props.lesson.is_quiz === '1' && !props.quizResult;
});

const handleMarkComplete = async () => {
  const result = await confirmMarkAsComplete();
  if (result.isConfirmed) {
    try {
      const response = await completeLesson(props.courseId, props.topicId, props.lessonId);
      await showLessonCompleted(response.msg);
      emit('mark-complete', response);
    } catch (error) {
      await showError(error.message || 'Failed to mark lesson as complete.');
    }
  }
};

const handleSubmitQuiz = async () => {
  if (!allAnswered.value) {
    await showIncompleteQuiz();
    return;
  }

  const result = await confirmSubmitQuiz();
  if (result.isConfirmed) {
    try {
      const response = await submitQuiz(props.courseId, props.topicId, props.lessonId, props.questions);
      await showQuizSubmitted(response.data.score);
      emit('submit-quiz', response);
    } catch (error) {
      await showError(error.message || 'Failed to submit quiz.');
    }
  }
};

const handleRetakeQuiz = async () => {
  const result = await confirmRetakeQuiz();
  if (result.isConfirmed) {
    emit('retake-quiz');
  }
};

const handleDownloadCertificate = async () => {
  try {
    await downloadCourseCertificate(props.courseId);
    await showCertificateDownloaded();
    emit('download-certificate');
  } catch (error) {
    await showError('Failed to download certificate.');
  }
};
</script>

<style scoped>
.lesson-actions {
  margin-top: 2rem;
}

.action-group {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.complete-lesson-btn, .submit-quiz-btn, .retake-btn, .certificate-btn {
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  min-width: 180px;
  justify-content: center;
}

.complete-lesson-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.complete-lesson-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.submit-quiz-btn {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submit-quiz-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.retake-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.retake-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.certificate-btn {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.certificate-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.complete-lesson-btn:disabled,
.submit-quiz-btn:disabled,
.retake-btn:disabled,
.certificate-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.completion-message {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.completion-card {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border: 2px solid #10b981;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
}

.completion-icon {
  font-size: 3rem;
  color: #10b981;
  margin-bottom: 1rem;
}

.completion-card h3 {
  color: #065f46;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.completion-card p {
  color: #047857;
  font-size: 1rem;
  margin: 0;
}

.quiz-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .complete-lesson-btn, .submit-quiz-btn, .retake-btn, .certificate-btn {
    min-width: 150px;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
  
  .quiz-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .completion-card {
    padding: 1.5rem;
  }
  
  .completion-icon {
    font-size: 2.5rem;
  }
  
  .completion-card h3 {
    font-size: 1.25rem;
  }
}
</style>
