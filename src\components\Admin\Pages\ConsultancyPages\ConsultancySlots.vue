<template>
  <div class="consultancy-slots-management">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="h3 mb-1">Consultancy Slots Management</h2>
        <p class="text-muted mb-0">Manage available consultancy time slots</p>
      </div>
      <button
        @click="showCreateModal = true"
        class="btn btn-primary"
      >
        <i class="fa fa-plus me-2"></i>
        Create Slot
      </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Slots</h6>
                <h4 class="mb-0">{{ stats.total_slots }}</h4>
              </div>
              <div class="bg-primary bg-opacity-10 p-3 rounded">
                <i class="fa fa-calendar text-primary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Active Slots</h6>
                <h4 class="mb-0">{{ stats.active_slots }}</h4>
              </div>
              <div class="bg-success bg-opacity-10 p-3 rounded">
                <i class="fa fa-clock text-success"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Today's Bookings</h6>
                <h4 class="mb-0">{{ stats.today_bookings }}</h4>
              </div>
              <div class="bg-warning bg-opacity-10 p-3 rounded">
                <i class="fa fa-users text-warning"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Upcoming</h6>
                <h4 class="mb-0">{{ stats.upcoming_bookings }}</h4>
              </div>
              <div class="bg-info bg-opacity-10 p-3 rounded">
                <i class="fa fa-eye text-info"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <select v-model="filters.type" class="form-select">
              <option value="">All Types</option>
              <option value="online">Online</option>
              <option value="in_person">In-Person</option>
            </select>
          </div>
          <div class="col-md-3">
            <input
              type="date"
              v-model="filters.date"
              class="form-control"
              placeholder="Filter by date"
            />
          </div>
          <div class="col-md-3">
            <select v-model="filters.country" class="form-select">
              <option value="">All Countries</option>
              <option v-for="country in countries" :key="country" :value="country">
                {{ country }}
              </option>
            </select>
          </div>
          <div class="col-md-3">
            <select v-model="filters.is_active" class="form-select">
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Slots Grid -->
    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <div v-else-if="slots.length > 0" class="row">
      <div v-for="slot in slots" :key="slot._id" class="col-lg-4 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
          <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <span
                :class="['badge', slot.is_active ? 'bg-success' : 'bg-secondary']"
                class="me-2"
              >
                {{ slot.is_active ? 'Active' : 'Inactive' }}
              </span>
              <span
                :class="[
                  'badge',
                  slot.type === 'online' ? 'bg-primary' : 'bg-purple'
                ]"
              >
                {{ slot.type === 'online' ? 'Online' : 'In-Person' }}
              </span>
              <span v-if="slot.is_full" class="badge bg-danger ms-2">
                Full
              </span>
            </div>
            <div class="dropdown">
              <button
                class="btn btn-sm btn-outline-secondary"
                type="button"
                data-bs-toggle="dropdown"
              >
                <i class="fa fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu">
                <li>
                  <a
                    @click="editSlot(slot)"
                    class="dropdown-item"
                    href="javascript:void(0)"
                  >
                    <i class="fa fa-edit me-2"></i>Edit
                  </a>
                </li>
                <li>
                  <a
                    @click="deleteSlot(slot._id)"
                    class="dropdown-item text-danger"
                    href="javascript:void(0)"
                  >
                    <i class="fa fa-trash me-2"></i>Delete
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <i class="fa fa-calendar me-2 text-muted"></i>
                <span>{{ formatDate(slot.date) }}</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <i class="fa fa-clock me-2 text-muted"></i>
                <span>{{ slot.start_time }} - {{ slot.end_time }}</span>
                <span class="badge bg-light text-dark ms-2">{{ slot.duration_minutes }}min</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <i class="fa fa-users me-2 text-muted"></i>
                <span>{{ slot.confirmed_bookings }}/{{ slot.max_capacity }} booked</span>
              </div>
              <div v-if="slot.type === 'in_person'" class="d-flex align-items-center">
                <i class="fa fa-map-marker me-2 text-muted"></i>
                <span>{{ slot.city }}, {{ slot.country }}</span>
              </div>
            </div>

            <!-- Bookings List -->
            <div v-if="slot.bookings && slot.bookings.length > 0" class="border-top pt-3">
              <h6 class="small text-muted mb-2">Bookings:</h6>
              <div v-for="booking in slot.bookings.slice(0, 2)" :key="booking.id" class="d-flex justify-content-between align-items-center mb-1">
                <span class="small">{{ booking.user.name }}</span>
                <span
                  :class="[
                    'badge',
                    booking.status === 'confirmed' ? 'bg-success' : 'bg-warning'
                  ]"
                >
                  {{ booking.status }}
                </span>
              </div>
              <small v-if="slot.bookings.length > 2" class="text-muted">
                +{{ slot.bookings.length - 2 }} more
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-5">
      <i class="fa fa-calendar fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">No slots found</h5>
      <p class="text-muted mb-3">Create your first consultancy slot to get started.</p>
      <button @click="showCreateModal = true" class="btn btn-primary">
        Create Slot
      </button>
    </div>

    <!-- Create/Edit Modal -->
    <div
      class="modal fade"
      :class="{ show: showCreateModal }"
      :style="{ display: showCreateModal ? 'block' : 'none' }"
      tabindex="-1"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              {{ editingSlot ? 'Edit Consultancy Slot' : 'Create New Consultancy Slot' }}
            </h5>
            <button
              type="button"
              class="btn-close"
              @click="closeModal"
            ></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveSlot">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Date *</label>
                  <input
                    type="date"
                    v-model="newSlot.date"
                    class="form-control"
                    required
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">Type *</label>
                  <select v-model="newSlot.type" class="form-select" required>
                    <option value="online">Online</option>
                    <option value="in_person">In-Person</option>
                  </select>
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Start Time *</label>
                  <input
                    type="time"
                    v-model="newSlot.start_time"
                    class="form-control"
                    required
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">End Time *</label>
                  <input
                    type="time"
                    v-model="newSlot.end_time"
                    class="form-control"
                    required
                  />
                </div>
              </div>

              <div class="mb-3">
                <label class="form-label">Max Capacity *</label>
                <input
                  type="number"
                  v-model="newSlot.max_capacity"
                  class="form-control"
                  min="1"
                  max="10"
                  required
                />
              </div>

              <!-- In-Person Fields -->
              <div v-if="newSlot.type === 'in_person'">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">Country *</label>
                    <select v-model="newSlot.country" class="form-select" required>
                      <option value="">Select Country</option>
                      <option v-for="country in countries" :key="country" :value="country">
                        {{ country }}
                      </option>
                    </select>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">City *</label>
                    <input
                      type="text"
                      v-model="newSlot.city"
                      class="form-control"
                      required
                    />
                  </div>
                </div>

                <div class="mb-3">
                  <label class="form-label">Address</label>
                  <input
                    type="text"
                    v-model="newSlot.address"
                    class="form-control"
                  />
                </div>

                <div class="mb-3">
                  <label class="form-label">Location Name</label>
                  <input
                    type="text"
                    v-model="newSlot.location"
                    class="form-control"
                    placeholder="e.g., Main Office, Conference Room A"
                  />
                </div>
              </div>

              <div class="mb-3">
                <label class="form-label">Notes</label>
                <textarea
                  v-model="newSlot.notes"
                  class="form-control"
                  rows="3"
                ></textarea>
              </div>

              <div class="form-check">
                <input
                  type="checkbox"
                  id="is_active"
                  v-model="newSlot.is_active"
                  class="form-check-input"
                />
                <label for="is_active" class="form-check-label">
                  Active (available for booking)
                </label>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeModal">
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="saveSlot"
              :disabled="loading"
            >
              {{ loading ? 'Saving...' : (editingSlot ? 'Update Slot' : 'Create Slot') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Backdrop -->
    <div
      v-if="showCreateModal"
      class="modal-backdrop fade show"
      @click="closeModal"
    ></div>
  </div>
</template>

<script>
import ConsultancyService from '@/services/ConsultancyService';

export default {
  name: 'AdminConsultancySlots',
  data() {
    return {
      slots: [],
      loading: false,
      showCreateModal: false,
      editingSlot: null,
      
      filters: {
        type: '',
        date: '',
        country: '',
        is_active: ''
      },
      
      newSlot: {
        date: '',
        start_time: '',
        end_time: '',
        type: 'online',
        location: '',
        country: '',
        city: '',
        address: '',
        max_capacity: 1,
        is_active: true,
        notes: '',
        meeting_link_template: ''
      },

      countries: [
        'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 
        'France', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Switzerland'
      ],

      stats: {
        total_slots: 0,
        active_slots: 0,
        today_bookings: 0,
        upcoming_bookings: 0
      }
    };
  },

  watch: {
    filters: {
      handler() {
        this.fetchSlots();
      },
      deep: true
    }
  },

  mounted() {
    this.fetchSlots();
    this.fetchStats();
  },

  methods: {
    async fetchSlots() {
      this.loading = true;
      try {
        const response = await ConsultancyService.admin.getAllSlots(this.filters);
        this.slots = response.data?.data || response.data || [];
      } catch (error) {
        console.error('Error fetching slots:', error);
        this.$toast?.error('Failed to fetch slots: ' + error.message);
      }
      this.loading = false;
    },

    async fetchStats() {
      try {
        const response = await ConsultancyService.admin.getStats();
        this.stats = response.data || {};
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Don't show error for stats as it's not critical
      }
    },

    async saveSlot() {
      this.loading = true;
      try {
        if (this.editingSlot) {
          // Update existing slot
          const slotData = { ...this.newSlot, id: this.editingSlot._id };
          await ConsultancyService.admin.updateSlot(slotData);
          this.$toast?.success('Slot updated successfully!');
        } else {
          // Create new slot
          await ConsultancyService.admin.createSlot(this.newSlot);
          this.$toast?.success('Slot created successfully!');
        }
        
        this.closeModal();
        this.fetchSlots();
        this.fetchStats();
      } catch (error) {
        console.error('Error saving slot:', error);
        this.$toast?.error('Failed to save slot: ' + error.message);
      }
      this.loading = false;
    },

    editSlot(slot) {
      this.editingSlot = slot;
      this.newSlot = { ...slot };
      this.showCreateModal = true;
    },

    async deleteSlot(slotId) {
      if (!confirm('Are you sure you want to delete this slot?')) return;
      
      try {
        await ConsultancyService.admin.deleteSlot(slotId);
        this.$toast?.success('Slot deleted successfully!');
        this.fetchSlots();
        this.fetchStats();
      } catch (error) {
        console.error('Error deleting slot:', error);
        this.$toast?.error('Failed to delete slot: ' + error.message);
      }
    },

    closeModal() {
      this.showCreateModal = false;
      this.editingSlot = null;
      this.newSlot = {
        date: '',
        start_time: '',
        end_time: '',
        type: 'online',
        location: '',
        country: '',
        city: '',
        address: '',
        max_capacity: 1,
        is_active: true,
        notes: '',
        meeting_link_template: ''
      };
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString();
    }
  }
};
</script>

<style scoped>
.bg-purple {
  background-color: #6f42c1 !important;
}

.modal.show {
  opacity: 1;
}

.consultancy-slots-management {
  padding: 1rem;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.badge {
  font-size: 0.75rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}
</style>