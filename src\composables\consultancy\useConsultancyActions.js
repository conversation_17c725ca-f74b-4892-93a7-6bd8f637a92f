// src/composables/consultancy/useConsultancyActions.js
import { ref, reactive } from 'vue';
import ApiService from '@/services/ApiService';
import { ConsultancyApiRoutes } from '@/ApiRoutes/consultancyApiRoutes';

export const useConsultancyActions = () => {
    const loading = ref(false);
    const error = ref(null);
    const success = ref(null);

    // Available slots management
    const availableSlots = ref([]);
    const slotsLoading = ref(false);

    // User bookings management
    const myBookings = ref([]);
    const bookingsLoading = ref(false);

    // User consultancy usage
    const consultancyUsage = ref(null);
    const usageLoading = ref(false);

    // Get available slots for booking
    const getAvailableSlots = async (filters = {}) => {
        slotsLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.User.GetAvailableSlots,
                filters
            );

            if (response.status === 200) {
                availableSlots.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch available slots';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            slotsLoading.value = false;
        }
    };

    // Book a consultancy session
    const bookSession = async (slotId, notes = '') => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.User.BookSession,
                { slot_id: slotId, notes }
            );

            if (response.status === 200) {
                success.value = 'Session booked successfully!';
                // Refresh data
                await Promise.all([
                    getMyBookings(),
                    getMyUsage(),
                    getAvailableSlots()
                ]);
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to book session';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Get user's bookings
    const getMyBookings = async (filters = {}) => {
        bookingsLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.User.GetMyBookings,
                filters
            );

            if (response.status === 200) {
                myBookings.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch bookings';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            bookingsLoading.value = false;
        }
    };

    // Cancel a booking
    const cancelBooking = async (bookingId, reason = '') => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.User.CancelBooking,
                { booking_id: bookingId, cancellation_reason: reason }
            );

            if (response.status === 200) {
                success.value = 'Booking cancelled successfully!';
                // Refresh data
                await Promise.all([
                    getMyBookings(),
                    getMyUsage(),
                    getAvailableSlots()
                ]);
                return { success: true };
            } else {
                error.value = response.error || 'Failed to cancel booking';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Get user's consultancy usage
    const getMyUsage = async () => {
        usageLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.GET(ConsultancyApiRoutes.User.GetMyUsage);

            if (response.status === 200) {
                consultancyUsage.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch usage data';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            usageLoading.value = false;
        }
    };

    // Get upcoming sessions
    const getUpcomingSessions = async () => {
        try {
            const response = await ApiService.GET(ConsultancyApiRoutes.User.GetUpcomingSessions);
            
            if (response.status === 200) {
                return { success: true, data: response.data };
            } else {
                return { success: false, error: response.error };
            }
        } catch (err) {
            return { success: false, error: 'Network error occurred' };
        }
    };

    // Check if user needs upgrade
    const checkUpgradeRequired = () => {
        if (!consultancyUsage.value) return true;
        return consultancyUsage.value.remaining_minutes <= 0;
    };

    // Get usage percentage
    const getUsagePercentage = () => {
        if (!consultancyUsage.value || consultancyUsage.value.total_minutes_allocated === 0) return 0;
        return (consultancyUsage.value.used_minutes / consultancyUsage.value.total_minutes_allocated) * 100;
    };

    // Clear messages
    const clearMessages = () => {
        error.value = null;
        success.value = null;
    };

    return {
        // State
        loading,
        error,
        success,
        availableSlots,
        slotsLoading,
        myBookings,
        bookingsLoading,
        consultancyUsage,
        usageLoading,

        // Actions
        getAvailableSlots,
        bookSession,
        getMyBookings,
        cancelBooking,
        getMyUsage,
        getUpcomingSessions,
        checkUpgradeRequired,
        getUsagePercentage,
        clearMessages,
    };
};
