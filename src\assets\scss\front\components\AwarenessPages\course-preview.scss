.modern-course-container {
    min-height: 100vh;
    background: #f8fafc;
}

/* Loading State */
.course-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
}

.loading-spinner {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 2rem;
}

.spinner-ring {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid #059669;
    border-radius: 50%;
    animation: ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #059669 transparent transparent transparent;
}

.spinner-ring:nth-child(1) { animation-delay: -0.45s; }
.spinner-ring:nth-child(2) { animation-delay: -0.3s; }
.spinner-ring:nth-child(3) { animation-delay: -0.15s; }

@keyframes ring {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.loading-text {
    color: #6b7280;
    margin: 0;
}

/* Course Header */
.course-header {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    padding: 2rem;
    border-radius: 24px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.course-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 2rem;
}

.course-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: white;
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 0.25rem;
}

.breadcrumb-current {
    color: white;
    font-weight: 500;
}

.course-title-section {
    flex: 1;
}

.course-main-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.course-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.course-meta {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.course-banner {
    width: 200px;
    height: 120px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    flex-shrink: 0;
}

.banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Layout */
.course-main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Sidebar */
.course-sidebar {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 2rem;
    max-height: calc(100vh - 4rem);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f8fafc;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-circle {
    position: relative;
    width: 48px;
    height: 48px;
}

.circular-chart {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e5e7eb;
    stroke-width: 3;
}

.circle-progress {
    fill: none;
    stroke: #059669;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.6s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #059669;
}

.topics-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Topic Accordion */
.topic-item {
    margin-bottom: 0.5rem;
}

.topic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    background: white;
}

.topic-header:hover {
    background: #f8fafc;
    border-color: #059669;
}

.topic-header.active {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(4, 120, 87, 0.1));
    border-color: #059669;
}

.topic-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.topic-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #059669, #047857);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.topic-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.topic-lessons-count {
    font-size: 0.75rem;
    color: #6b7280;
}

.topic-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.topic-progress {
    font-size: 0.75rem;
    color: #059669;
    font-weight: 600;
}

.expand-icon {
    transition: transform 0.3s ease;
    color: #6b7280;
}

.expand-icon.rotated {
    transform: rotate(180deg);
}

/* Topic Lessons */
.topic-lessons {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.topic-lessons.expanded {
    max-height: 500px;
}

.no-lessons {
    padding: 1rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.lessons-list {
    padding: 0.5rem 0;
}

.lesson-item {
    margin-bottom: 0.25rem;
}

.lesson-link,
.lesson-locked {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    border-left: 3px solid transparent;
}

.lesson-link:hover {
    background: #f8fafc;
    border-left-color: #059669;
}

.lesson-item.current .lesson-link {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(4, 120, 87, 0.1));
    border-left-color: #059669;
    color: #059669;
}

.lesson-item.completed .lesson-status i {
    color: #059669;
}

.lesson-item.locked .lesson-locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.lesson-status {
    width: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lesson-status i {
    color: #9ca3af;
    transition: color 0.3s ease;
}

.lesson-content {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-weight: 500;
    color: #374151;
    display: block;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lesson-type {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #6b7280;
}

.lesson-type.locked {
    color: #ef4444;
}

.lesson-indicator {
    opacity: 0;
    transition: opacity 0.3s ease;
    color: #059669;
}

.lesson-item.current .lesson-indicator {
    opacity: 1;
}

/* Main Content */
.course-content {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    min-height: 600px;
    overflow: hidden;
}

/* Course Overview */
.course-overview {
    padding: 2rem;
}

.overview-card {
    max-width: 800px;
    margin: 0 auto;
}

.overview-header {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    align-items: center;
}

.course-image {
    width: 200px;
    height: 120px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.course-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-info {
    flex: 1;
}

.overview-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.overview-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.course-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #059669;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Action Sections */
.getting-started,
.continue-learning,
.course-completed {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    border: 1px solid #e5e7eb;
}

.getting-started-title,
.continue-title,
.completed-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.getting-started-title i {
    color: #3b82f6;
}

.continue-title i {
    color: #f59e0b;
}

.completed-title i {
    color: #fbbf24;
}

.getting-started-text,
.continue-text,
.completed-text {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.start-learning-btn,
.continue-btn,
.certificate-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.start-learning-btn:hover,
.continue-btn:hover,
.certificate-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.certificate-btn {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.certificate-btn:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

/* Lesson Content */
.lesson-content {
    height: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .course-main-content {
        grid-template-columns: 300px 1fr;
        gap: 1.5rem;
    }

    .course-main-title {
        font-size: 2rem;
    }

    .course-banner {
        width: 150px;
        height: 90px;
    }
}

@media (max-width: 1024px) {
    .course-main-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .course-sidebar {
        position: static;
        max-height: none;
        order: 2;
    }

    .course-content {
        order: 1;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .course-banner {
        width: 200px;
        height: 120px;
    }

    .course-meta {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .modern-course-container {
        padding: 0;
    }

    .course-header {
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-radius: 0;
    }

    .course-main-title {
        font-size: 1.75rem;
    }

    .course-description {
        font-size: 1rem;
    }

    .course-meta {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .meta-item {
        justify-content: center;
    }

    .course-banner {
        width: 100%;
        max-width: 250px;
        height: 150px;
    }

    .sidebar-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .topics-container {
        padding: 0.75rem;
    }

    .topic-header {
        padding: 0.75rem;
    }

    .topic-title {
        font-size: 0.875rem;
    }

    .lesson-link,
    .lesson-locked {
        padding: 0.625rem 0.75rem;
    }

    .course-overview {
        padding: 1rem;
    }

    .overview-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .course-image {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }

    .overview-title {
        font-size: 1.5rem;
    }

    .course-stats {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .getting-started,
    .continue-learning,
    .course-completed {
        padding: 1.5rem;
    }

    .getting-started-title,
    .continue-title,
    .completed-title {
        font-size: 1.25rem;
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .course-header {
        padding: 1rem;
    }

    .course-main-title {
        font-size: 1.5rem;
    }

    .breadcrumb-link {
        font-size: 0.75rem;
    }

    .breadcrumb-current {
        font-size: 0.75rem;
    }

    .sidebar-title {
        font-size: 1rem;
    }

    .progress-circle {
        width: 36px;
        height: 36px;
    }

    .topic-header {
        padding: 0.625rem;
    }

    .topic-icon {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .topic-title {
        font-size: 0.8125rem;
    }

    .topic-lessons-count {
        font-size: 0.6875rem;
    }

    .lesson-link,
    .lesson-locked {
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .lesson-title {
        font-size: 0.875rem;
    }

    .lesson-type {
        font-size: 0.6875rem;
    }

    .overview-title {
        font-size: 1.25rem;
    }

    .course-stats {
        gap: 0.75rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .start-learning-btn,
    .continue-btn,
    .certificate-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }
}

/* Enhanced Animations */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-sidebar {
    animation: slideInLeft 0.6s ease-out;
}

.course-content {
    animation: slideInRight 0.6s ease-out 0.2s both;
}

.topic-item {
    animation: fadeInUp 0.4s ease-out;
}

.topic-item:nth-child(1) { animation-delay: 0.1s; }
.topic-item:nth-child(2) { animation-delay: 0.2s; }
.topic-item:nth-child(3) { animation-delay: 0.3s; }
.topic-item:nth-child(4) { animation-delay: 0.4s; }

/* Focus States for Accessibility */
.topic-header:focus,
.lesson-link:focus,
.start-learning-btn:focus,
.continue-btn:focus,
.certificate-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* Scrollbar Styling */
.topics-container::-webkit-scrollbar {
    width: 6px;
}

.topics-container::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 3px;
}

.topics-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.topics-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .topic-header,
    .lesson-link,
    .course-sidebar,
    .course-content {
        border-width: 2px;
    }

    .topic-icon,
    .lesson-status i {
        filter: contrast(1.2);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .course-sidebar,
    .course-content,
    .topic-item {
        animation: none;
    }

    .expand-icon,
    .circle-progress,
    .topic-header,
    .lesson-link {
        transition: none;
    }

    @keyframes ring {
        from, to {
            transform: rotate(0deg);
        }
    }
}

/* Print Styles */
@media print {
    .course-header {
        background: white !important;
        color: black !important;
    }

    .course-sidebar {
        display: none;
    }

    .course-main-content {
        grid-template-columns: 1fr;
    }

    .start-learning-btn,
    .continue-btn,
    .certificate-btn {
        background: white !important;
        color: black !important;
        border: 1px solid black;
    }
}
