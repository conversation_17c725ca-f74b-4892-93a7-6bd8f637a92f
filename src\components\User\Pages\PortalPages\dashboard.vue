<!-- src/components/User/Pages/PortalPages/dashboard.vue -->
<template>
    <div class="modern-dashboard">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <div class="welcome-content">
                <div class="welcome-text">
                    <h1 class="welcome-title">
                        Welcome back, <span class="user-name">{{ UserInfo?.name || 'User' }}</span>
                    </h1>
                    <p class="welcome-subtitle">
                        Manage your AI risk evaluations and fairness analysis from your dashboard
                    </p>
                </div>
                <div class="welcome-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.riskEvaluations }}</div>
                            <div class="stat-label">Risk Evaluations</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fa fa-balance-scale"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.fairnessAnalysis }}</div>
                            <div class="stat-label">Fairness Analysis</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fa fa-graduation-cap"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">{{ stats.completedCourses }}</div>
                            <div class="stat-label">Started Courses</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Grid -->
        <div class="quick-actions-section">
            <h2 class="section-title">Quick Actions</h2>
            <div class="actions-grid">
                <router-link 
                    :to="{ name: 'StartRiskEvaluation' }" 
                    class="action-card risk-evaluation"
                    @click.native="() => handleRouteClick('risk')"
                >
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-shield"></i>
                        </div>
                        <div class="card-badge">Risk</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Start Risk Evaluation</h3>
                        <p class="card-description">
                            Evaluate AI systems for potential risks and vulnerabilities
                        </p>
                    </div>
                    <div class="card-footer">
                        <span class="action-text">Start Evaluation</span>
                        <i class="fa fa-arrow-right"></i>
                    </div>
                </router-link>

                <router-link 
                    :to="{ name: 'StartFairDecisionAnalysis' }" 
                    class="action-card fairness-analysis"
                    @click.native="() => handleRouteClick('fair')"
                >
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-balance-scale"></i>
                        </div>
                        <div class="card-badge">Fairness</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Fairness Analysis</h3>
                        <p class="card-description">
                            Analyze decision-making processes for bias and fairness
                        </p>
                    </div>
                    <div class="card-footer">
                        <span class="action-text">Start Analysis</span>
                        <i class="fa fa-arrow-right"></i>
                    </div>
                </router-link>

                <router-link 
                    :to="{ name: 'CourseList' }" 
                    class="action-card awareness"
                >
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-lightbulb-o"></i>
                        </div>
                        <div class="card-badge">Learning</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Awareness Training</h3>
                        <p class="card-description">
                            Enhance your knowledge with AI awareness courses
                        </p>
                    </div>
                    <div class="card-footer">
                        <span class="action-text">Browse Courses</span>
                        <i class="fa fa-arrow-right"></i>
                    </div>
                </router-link>

                <router-link 
                    :to="{ name: 'Pricing' }" 
                    class="action-card premium"
                    v-if="Subscription.package_price === 0"
                >
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fa fa-star"></i>
                        </div>
                        <div class="card-badge premium-badge">Upgrade</div>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Upgrade Package</h3>
                        <p class="card-description">
                            Unlock advanced features and detailed analysis
                        </p>
                    </div>
                    <div class="card-footer">
                        <span class="action-text">View Plans</span>
                        <i class="fa fa-arrow-right"></i>
                    </div>
                </router-link>
            </div>
        </div>

        <!-- Recent Activity Section -->
        <div class="recent-activity-section" v-if="recentEvaluations.length > 0">
            <h2 class="section-title">Recent Evaluations</h2>
            <div class="activity-grid">
                <div 
                    v-for="evaluation in recentEvaluations" 
                    :key="evaluation._id"
                    class="activity-card"
                    @click="viewEvaluation(evaluation)"
                >
                    <div class="activity-header">
                        <div class="activity-icon" :class="getEvaluationTypeClass(evaluation.category)">
                            <i :class="getEvaluationIcon(evaluation.category)"></i>
                        </div>
                        <div class="activity-meta">
                            <span class="activity-date">{{ formatDate(evaluation.created_at) }}</span>
                            <span class="activity-type">{{ getEvaluationType(evaluation.category) }}</span>
                        </div>
                    </div>
                    <div class="activity-content">
                        <h4 class="activity-title">{{ evaluation.project.name }}</h4>
                        <p class="activity-description" v-if="evaluation.project.desc">
                            {{ evaluation.project.desc }}
                        </p>
                    </div>
                    <div class="activity-footer">
                        <span class="view-report">View Report</span>
                        <i class="fa fa-external-link"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div class="empty-state" v-if="recentEvaluations.length === 0">
            <div class="empty-content">
                <div class="empty-icon">
                    <i class="fa fa-chart-bar"></i>
                </div>
                <h3 class="empty-title">No evaluations yet</h3>
                <p class="empty-description">
                    Start your first AI risk evaluation or fairness analysis to see your results here
                </p>
                <div class="empty-actions">
                    <router-link :to="{ name: 'StartRiskEvaluation' }" class="btn-primary">
                        Start Risk Evaluation
                    </router-link>
                    <router-link :to="{ name: 'StartFairDecisionAnalysis' }" class="btn-secondary">
                        Start Fairness Analysis
                    </router-link>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";

export default {
    name: 'ModernDashboard',
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            Subscription: JSON.parse(localStorage.getItem('Subscription')),
            loading: false,
            recentEvaluations: [],
            stats: {
                riskEvaluations: 0,
                fairnessAnalysis: 0,
                completedCourses: 0
            }
        }
    },
    methods: {
        async loadDashboardData() {
            this.loading = true;
            try {
                // Load recent evaluations
                ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
                    if (res.status === 200) {
                        this.recentEvaluations = res.data.slice(0, 6); // Show last 6
                        this.calculateStats(res.data);
                    }
                });

                // Load awareness evaluations for completed courses count
                ApiService.GET(ApiRoutes.AwarenessEvaluations, (res) => {
                    if (res.status === 200) {
                        this.stats.completedCourses = res.data.length;
                    }
                });
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            } finally {
                this.loading = false;
            }
        },

        calculateStats(evaluations) {
            const riskEvals = evaluations.filter(e => 
                ['et', 'eta', 'nt'].includes(e.category)
            );
            const fairnessEvals = evaluations.filter(e => 
                ['fd', 'eta-fd'].includes(e.category)
            );

            this.stats.riskEvaluations = riskEvals.length;
            this.stats.fairnessAnalysis = fairnessEvals.length;
        },

        viewEvaluation(evaluation) {
            this.$router.push({
                name: 'EvaluationReport',
                params: { evaluation_id: evaluation._id }
            });
        },

        getEvaluationType(category) {
            const types = {
                'et': 'General Risk',
                'eta': 'Industry Risk',
                'nt': 'Non-Tech Risk',
                'fd': 'General Fairness',
                'eta-fd': 'Industry Fairness'
            };
            return types[category] || 'Evaluation';
        },

        getEvaluationIcon(category) {
            if (['et', 'eta', 'nt'].includes(category)) {
                return 'fa fa-shield';
            }
            return 'fa fa-balance-scale';
        },

        getEvaluationTypeClass(category) {
            if (['et', 'eta', 'nt'].includes(category)) {
                return 'risk-type';
            }
            return 'fairness-type';
        },

        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
            });
        },
        handleRouteClick(type) {
            localStorage.setItem('risk_analysis', type);
        }
    },
    created() {
        this.loadDashboardData();
    },
    mounted() {
        window.scrollTo(0, 0);
    }
}
</script>

<style scoped>
.modern-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
}

/* Welcome Section */
.welcome-section {
    background: linear-gradient(135deg, #7925c7 0%, #a855f7 100%);
    border-radius: 24px;
    padding: 3rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.welcome-text {
    flex: 1;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.user-name {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.welcome-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    min-width: 120px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Section Titles */
.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 2rem;
    position: relative;
    padding-left: 1rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 2rem;
    background: linear-gradient(135deg, #7925c7, #a855f7);
    border-radius: 2px;
}

/* Quick Actions */
.quick-actions-section {
    margin-bottom: 3rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.action-card:hover::before {
    left: 100%;
}

.action-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #7925c7;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.risk-evaluation .card-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.fairness-analysis .card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.awareness .card-icon {
    background: linear-gradient(135deg, #059669, #047857);
}

.premium .card-icon {
    background: linear-gradient(135deg, #7925c7, #a855f7);
}

.card-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-evaluation .card-badge {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.fairness-analysis .card-badge {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

.awareness .card-badge {
    background: rgba(11, 245, 175, 0.1);
    color: #047857;
}

.premium-badge {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
}

.card-content {
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.card-description {
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #7925c7;
    font-weight: 600;
}

.card-footer i {
    transition: transform 0.3s ease;
}

.action-card:hover .card-footer i {
    transform: translateX(4px);
}

/* Recent Activity */
.recent-activity-section {
    margin-bottom: 3rem;
}

.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.activity-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.activity-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.08);
    border-color: #7925c7;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.activity-icon.risk-type {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.activity-icon.fairness-type {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.activity-meta {
    text-align: right;
}

.activity-date {
    display: block;
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.activity-type {
    font-size: 0.75rem;
    font-weight: 600;
    color: #7925c7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.activity-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.activity-description {
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0 0 1rem 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.activity-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #7925c7;
    font-size: 0.875rem;
    font-weight: 500;
}

.activity-footer i {
    transition: transform 0.3s ease;
}

.activity-card:hover .activity-footer i {
    transform: translateX(2px);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #64748b;
    font-size: 2rem;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.empty-description {
    color: #64748b;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #7925c7, #a855f7);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(121, 37, 199, 0.3);
}

.btn-secondary {
    background: white;
    color: #7925c7;
    border: 2px solid #7925c7;
}

.btn-secondary:hover {
    background: #7925c7;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .welcome-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .welcome-stats {
        justify-content: center;
    }
    
    .welcome-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        padding: 0;
    }
    
    .welcome-section {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .welcome-title {
        font-size: 1.75rem;
    }
    
    .welcome-stats {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }
    
    .stat-card {
        min-width: auto;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .action-card {
        padding: 1.5rem;
    }
    
    .activity-grid {
        grid-template-columns: 1fr;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .welcome-section {
        padding: 1.5rem 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .action-card,
    .activity-card {
        padding: 1.25rem;
    }
    
    .card-title {
        font-size: 1.125rem;
    }
}
</style>