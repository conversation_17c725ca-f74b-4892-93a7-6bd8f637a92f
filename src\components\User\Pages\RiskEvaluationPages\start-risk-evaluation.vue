<!-- src/components/User/Pages/RiskEvaluationPages/start-risk-evaluation.vue -->
<template>
    <div class="start-evaluation-page">
        <!-- Premium Warning -->
        <div v-if="risk_form_check() === 0" class="premium-section">
            <div class="premium-card">
                <div class="premium-icon">
                    <i class="fa fa-shield"></i>
                </div>
                <div class="premium-content">
                    <h2 class="premium-title">Premium Risk Evaluation</h2>
                    <p class="premium-description">
                        Unlock advanced risk evaluation capabilities with our premium plan. 
                        Get comprehensive analysis, detailed reports, and priority support.
                    </p>
                    <div class="premium-features">
                        <div class="feature-item">
                            <i class="fa fa-check"></i>
                            <span>Unlimited risk evaluations</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa fa-check"></i>
                            <span>Advanced AI-powered analysis</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa fa-check"></i>
                            <span>Detailed mitigation strategies</span>
                        </div>
                        <div class="feature-item">
                            <i class="fa fa-check"></i>
                            <span>Downloadable certificates</span>
                        </div>
                    </div>
                    <div class="premium-actions">
                        <router-link :to="{ name: 'Pricing' }" class="btn-premium">
                            <i class="fa fa-crown"></i>
                            Choose Premium Plan
                        </router-link>
                        <router-link :to="{ name: 'RiskEvaluation' }" class="btn-back">
                            <i class="fa fa-arrow-left"></i>
                            Back to Dashboard
                        </router-link>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Form -->
        <div v-else class="evaluation-form-section">
            <div class="form-container">
                <!-- Header -->
                <div class="form-header">
                    <div class="header-icon">
                        <i class="fa fa-shield"></i>
                    </div>
                    <div class="header-content">
                        <h1 class="form-title">Start Risk Evaluation</h1>
                        <p class="form-subtitle">
                            Provide basic information about your project to begin the evaluation process
                        </p>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="submitForm" class="evaluation-form">
                    <div class="form-grid">
                        <!-- Project Name -->
                        <div class="form-group full-width">
                            <label class="form-label">
                                <i class="fa fa-project-diagram"></i>
                                Project Title
                                <span class="required">*</span>
                            </label>
                            <div class="input-container">
                                <input 
                                    type="text" 
                                    class="form-input"
                                    v-model="project.name"
                                    @keyup="name_validate"
                                    placeholder="Enter your project title (max 30 characters)"
                                    maxlength="30"
                                    required
                                >
                                <div class="char-counter">
                                    <span :class="{ 'text-warning': project.name.length > 25, 'text-danger': project.name.length >= 30 }">
                                        {{ project.name.length }}/30
                                    </span>
                                </div>
                            </div>
                            <p class="form-hint">
                                Choose a descriptive name that identifies your project clearly
                            </p>
                        </div>

                        <!-- Website -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fa fa-globe"></i>
                                Website URL
                                <span class="optional">(Optional)</span>
                            </label>
                            <input 
                                type="url" 
                                class="form-input"
                                v-model="project.website"
                                placeholder="https://example.com"
                            >
                            <p class="form-hint">
                                If applicable, provide the website related to this project
                            </p>
                        </div>

                        <!-- Analysis Type -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fa fa-chart-line"></i>
                                Analysis Type
                            </label>
                            <div class="analysis-type-display">
                                <div class="type-badge type-risk">
                                    <i class="fa fa-shield"></i>
                                    <span>Risk Evaluation</span>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group full-width">
                            <label class="form-label">
                                <i class="fa fa-align-left"></i>
                                Project Description
                                <span class="optional">(Optional)</span>
                            </label>
                            <div class="textarea-container">
                                <textarea 
                                    class="form-textarea"
                                    v-model="project.desc"
                                    @keyup="desc_validate"
                                    placeholder="Describe your project, its purpose, and evaluation goals (max 200 characters)"
                                    maxlength="200"
                                    rows="4"
                                ></textarea>
                                <div class="char-counter">
                                    <span :class="{ 'text-warning': project.desc.length > 180, 'text-danger': project.desc.length >= 200 }">
                                        {{ project.desc.length }}/200
                                    </span>
                                </div>
                            </div>
                            <p class="form-hint">
                                Provide context about what you want to evaluate and your goals
                            </p>
                        </div>
                    </div>

                    <!-- Quick Info Card -->
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fa fa-lightbulb"></i>
                        </div>
                        <div class="info-content">
                            <h3 class="info-title">What happens next?</h3>
                            <div class="info-steps">
                                <div class="info-step">
                                    <span class="step-dot">1</span>
                                    <span>Answer assessment questions</span>
                                </div>
                                <div class="info-step">
                                    <span class="step-dot">2</span>
                                    <span>AI analyzes your responses</span>
                                </div>
                                <div class="info-step">
                                    <span class="step-dot">3</span>
                                    <span>Receive detailed report</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <router-link 
                            :to="{ name: 'RiskEvaluation' }" 
                            class="btn-secondary"
                        >
                            <i class="fa fa-arrow-left"></i>
                            Back to Dashboard
                        </router-link>
                        <button type="submit" class="btn-primary">
                            <span>Continue</span>
                            <i class="fa fa-arrow-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            UserInfo: JSON.parse(localStorage.getItem('UserInfo')),
            Subscription: JSON.parse(localStorage.getItem('Subscription')),
            risk_evaluation: {
                category: '',
                project: null,
                evaluation_sector: null,
                evaluation_sub_sector: null,
                answers: {}
            },
            project: {
                name: '',
                website: '',
                desc: '',
            }
        }
    },
    methods: {
        name_validate() {
            if (this.project.name.length > 30) {
                this.project.name = this.project.name.substring(0, 30);
            }
        },
        desc_validate() {
            if (this.project.desc.length > 200) {
                this.project.desc = this.project.desc.substring(0, 200);
            }
        },
        submitForm: function () {
            this.risk_evaluation.project = this.project;
            localStorage.setItem('risk_evaluation', JSON.stringify(this.risk_evaluation));
            this.$router.push({name: 'AskAiSystems'})
        },
        risk_form_check() {
            // Risk evaluation is always available for now
            return 1;
            // Uncomment below to restrict based on subscription
            // return this.Subscription.package_price !== 0 ? 1 : 0;
        }
    },
    created() {
        // Set analysis type to risk
        localStorage.setItem('risk_analysis', 'risk');
        
        if (localStorage.getItem('risk_evaluation') != null) {
            this.risk_evaluation = JSON.parse(localStorage.getItem('risk_evaluation'));
            this.project = this.risk_evaluation.project || { name: '', website: '', desc: '' };
        }
    },
    mounted() {
        window.scrollTo(0, 0);
    },
}
</script>

<style scoped>
.start-evaluation-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Progress Header */
.progress-header {
    margin-bottom: 3rem;
}

.progress-container {
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.step-number {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
    border: 3px solid #e5e7eb;
    background: white;
    color: #9ca3af;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-color: #ef4444;
    color: white;
    transform: scale(1.1);
}

.step-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    transition: color 0.3s ease;
}

.progress-step.active .step-label {
    color: #ef4444;
}

.progress-line {
    width: 120px;
    height: 3px;
    background: #e5e7eb;
    margin: 0 1rem;
}

/* Premium Section */
.premium-section {
    margin-bottom: 2rem;
}

.premium-card {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.3);
}

.premium-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.premium-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.premium-content {
    position: relative;
    z-index: 1;
}

.premium-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.premium-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    justify-content: center;
}

.feature-item i {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.premium-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-premium {
    background: white;
    color: #f59e0b;
    padding: 1rem 2.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-premium:hover {
    background: #fef3c7;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #d97706;
    text-decoration: none;
}

.btn-back {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-back:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Form Section */
.evaluation-form-section {
    margin-bottom: 2rem;
}

.form-container {
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    padding: 2.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    overflow: hidden;
}

.form-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    position: relative;
    z-index: 1;
}

.header-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.form-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

/* Form Styles */
.evaluation-form {
    padding: 2.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-label i {
    color: #ef4444;
}

.required {
    color: #ef4444;
    font-weight: 700;
}

.optional {
    color: #9ca3af;
    font-weight: 400;
    text-transform: none;
    font-size: 0.75rem;
}

.input-container,
.textarea-container {
    position: relative;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #ef4444;
    background: white;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.char-counter {
    position: absolute;
    top: 1rem;
    right: 1.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.text-warning {
    color: #f59e0b;
}

.text-danger {
    color: #ef4444;
}

.form-hint {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

/* Analysis Type Display */
.analysis-type-display {
    padding: 1rem 0;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    color: white;
}

.type-risk {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* Info Card */
.info-card {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.info-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.info-content {
    flex: 1;
}

.info-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #374151;
    margin-bottom: 1rem;
}

.info-steps {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #6b7280;
}

.step-dot {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.btn-primary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 1rem 2.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.btn-secondary {
    background: white;
    color: #6b7280;
    padding: 1rem 2rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .start-evaluation-page {
        padding: 1rem;
    }
    
    .progress-container {
        padding: 1.5rem;
    }
    
    .progress-step {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .progress-line {
        width: 60px;
        margin: 0 0.5rem;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .step-label {
        font-size: 0.75rem;
        text-align: center;
    }
    
    .premium-card {
        padding: 2rem 1.5rem;
    }
    
    .premium-title {
        font-size: 1.875rem;
    }
    
    .premium-features {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .premium-actions {
        flex-direction: column;
    }
    
    .btn-premium,
    .btn-back {
        width: 100%;
        justify-content: center;
    }
    
    .form-header {
        padding: 2rem 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .header-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
    
    .evaluation-form {
        padding: 1.5rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .info-card {
        flex-direction: column;
        gap: 1rem;
    }
    
    .info-steps {
        gap: 0.5rem;
    }
    
    .form-actions {
        flex-direction: column-reverse;
        gap: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .progress-container {
        padding: 1rem;
    }
    
    .progress-line {
        width: 40px;
    }
    
    .premium-title {
        font-size: 1.5rem;
    }
    
    .premium-description {
        font-size: 1rem;
    }
    
    .form-header {
        padding: 1.5rem 1rem;
    }
    
    .evaluation-form {
        padding: 1rem;
    }
    
    .char-counter {
        position: static;
        margin-top: 0.5rem;
        align-self: flex-end;
    }
}

/* Enhanced animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-container {
    animation: slideInUp 0.6s ease-out;
}

.premium-card {
    animation: slideInUp 0.6s ease-out 0.2s both;
}

.progress-header {
    animation: slideInUp 0.6s ease-out 0.1s both;
}

/* Focus states for accessibility */
.form-input:focus,
.form-textarea:focus,
.btn-primary:focus,
.btn-secondary:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

.btn-premium:focus,
.btn-back:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}
</style>