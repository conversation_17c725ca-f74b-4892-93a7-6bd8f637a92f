<template>
  <div class="consultancy-dashboard">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="h3 mb-1">Consultancy Dashboard</h2>
        <p class="text-muted mb-0">Overview of consultancy system</p>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Slots</h6>
                <h4 class="mb-0">{{ stats.total_slots }}</h4>
              </div>
              <div class="bg-primary bg-opacity-10 p-3 rounded">
                <i class="fa fa-calendar text-primary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Bookings</h6>
                <h4 class="mb-0">{{ stats.total_bookings }}</h4>
              </div>
              <div class="bg-success bg-opacity-10 p-3 rounded">
                <i class="fa fa-check text-success"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Today's Sessions</h6>
                <h4 class="mb-0">{{ stats.today_bookings }}</h4>
              </div>
              <div class="bg-warning bg-opacity-10 p-3 rounded">
                <i class="fa fa-users text-warning"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Revenue This Month</h6>
                <h4 class="mb-0">${{ stats.monthly_revenue }}</h4>
              </div>
              <div class="bg-info bg-opacity-10 p-3 rounded">
                <i class="fa fa-dollar-sign text-info"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">Quick Actions</h5>
          </div>
          <div class="card-body">
            <div class="d-grid gap-2">
              <router-link 
                :to="{ name: 'AdminConsultancySlots' }" 
                class="btn btn-outline-primary"
              >
                <i class="fa fa-clock me-2"></i>
                Manage Time Slots
              </router-link>
              <router-link 
                :to="{ name: 'AdminConsultancyBookings' }" 
                class="btn btn-outline-success"
              >
                <i class="fa fa-calendar-check me-2"></i>
                View All Bookings
              </router-link>
              <button class="btn btn-outline-info" @click="exportData">
                <i class="fa fa-download me-2"></i>
                Export Reports
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card border-0 shadow-sm">
          <div class="card-header bg-transparent border-0">
            <h5 class="mb-0">Recent Activity</h5>
          </div>
          <div class="card-body">
            <div v-if="recentActivity.length > 0" class="list-group list-group-flush">
              <div 
                v-for="activity in recentActivity.slice(0, 5)" 
                :key="activity.id"
                class="list-group-item border-0 px-0"
              >
                <div class="d-flex justify-content-between align-items-start">
                  <div>
                    <h6 class="mb-1">{{ activity.title }}</h6>
                    <p class="mb-1 small text-muted">{{ activity.description }}</p>
                  </div>
                  <small class="text-muted">{{ formatTime(activity.time) }}</small>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-3">
              <i class="fa fa-clock fa-2x text-muted mb-2"></i>
              <p class="text-muted">No recent activity</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Upcoming Sessions -->
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Upcoming Sessions Today</h5>
        <router-link :to="{ name: 'AdminConsultancyBookings' }" class="btn btn-sm btn-primary">
          View All
        </router-link>
      </div>
      <div class="card-body">
        <div v-if="upcomingSessions.length > 0" class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Time</th>
                <th>Client</th>
                <th>Type</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="session in upcomingSessions" :key="session.id">
                <td>
                  <strong>{{ session.time }}</strong>
                  <br>
                  <small class="text-muted">{{ session.duration }}min</small>
                </td>
                <td>
                  <div>
                    <strong>{{ session.client_name }}</strong>
                    <br>
                    <small class="text-muted">{{ session.client_email }}</small>
                  </div>
                </td>
                <td>
                  <span 
                    :class="session.type === 'online' ? 'badge bg-primary' : 'badge bg-purple'"
                  >
                    {{ session.type === 'online' ? 'Online' : 'In-Person' }}
                  </span>
                </td>
                <td>
                  <span :class="getStatusColor(session.status)" class="badge">
                    {{ session.status }}
                  </span>
                </td>
                <td>
                  <button 
                    v-if="session.type === 'online'" 
                    class="btn btn-sm btn-outline-primary me-1"
                    @click="sendMeetingLink(session.id)"
                  >
                    <i class="fa fa-link"></i>
                  </button>
                  <button class="btn btn-sm btn-outline-secondary">
                    <i class="fa fa-eye"></i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else class="text-center py-4">
          <i class="fa fa-calendar fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">No sessions scheduled for today</h5>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ConsultancyService from '@/services/ConsultancyService';

export default {
  name: 'AdminConsultancyDashboard',
  data() {
    return {
      loading: false,
      stats: {
        total_slots: 0,
        total_bookings: 0,
        today_bookings: 0,
        monthly_revenue: 0
      },
      recentActivity: [],
      upcomingSessions: []
    };
  },

  mounted() {
    this.fetchDashboardData();
  },

  methods: {
    async fetchDashboardData() {
      this.loading = true;
      try {
        // Simulate API calls
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        this.stats = {
          total_slots: 45,
          total_bookings: 128,
          today_bookings: 8,
          monthly_revenue: 2450
        };

        this.recentActivity = [
          {
            id: 1,
            title: 'New booking created',
            description: 'John Doe booked a 1-hour session',
            time: new Date(Date.now() - 1000 * 60 * 30)
          },
          {
            id: 2,
            title: 'Session completed',
            description: 'Jane Smith completed consultation',
            time: new Date(Date.now() - 1000 * 60 * 60 * 2)
          },
          {
            id: 3,
            title: 'New time slot added',
            description: 'Added online slot for tomorrow',
            time: new Date(Date.now() - 1000 * 60 * 60 * 4)
          }
        ];

        this.upcomingSessions = [
          {
            id: 1,
            time: '10:00 AM',
            duration: 60,
            client_name: 'John Doe',
            client_email: '<EMAIL>',
            type: 'online',
            status: 'confirmed'
          },
          {
            id: 2,
            time: '2:00 PM',
            duration: 90,
            client_name: 'Jane Smith',
            client_email: '<EMAIL>',
            type: 'in_person',
            status: 'confirmed'
          }
        ];
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
      this.loading = false;
    },

    getStatusColor(status) {
      const colors = {
        confirmed: 'bg-success',
        pending: 'bg-warning',
        cancelled: 'bg-danger',
        completed: 'bg-primary'
      };
      return colors[status] || 'bg-secondary';
    },

    formatTime(date) {
      const now = new Date();
      const diff = now - date;
      const minutes = Math.floor(diff / (1000 * 60));
      
      if (minutes < 60) return `${minutes}m ago`;
      if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
      return `${Math.floor(minutes / 1440)}d ago`;
    },

    sendMeetingLink(sessionId) {
      // Implement send meeting link logic
      this.$toast?.success('Meeting link sent successfully!');
    },

    exportData() {
      // Implement export functionality
      this.$toast?.info('Exporting data...');
    }
  }
};
</script>

<style scoped>
.consultancy-dashboard {
  padding: 1rem;
}

.bg-purple {
  background-color: #6f42c1 !important;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.list-group-item:last-child {
  border-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
}

.btn-outline-primary,
.btn-outline-success,
.btn-outline-info {
  border-width: 1px;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}
</style>