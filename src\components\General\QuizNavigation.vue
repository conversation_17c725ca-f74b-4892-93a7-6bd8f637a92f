<template>
  <div class="quiz-navigation">
    <button 
      @click="handlePrevious" 
      class="nav-btn prev-btn" 
      :disabled="currentIndex === 0"
      :class="{ 'disabled': currentIndex === 0 }"
    >
      <i class="fa fa-chevron-left"></i>
      Previous
    </button>
    
    <div class="nav-indicators">
      <div 
        v-for="(question, index) in questions" 
        :key="index"
        class="nav-dot"
        :class="{ 
          'active': index === currentIndex,
          'answered': question.answer
        }"
        @click="handleGoToQuestion(index)"
        :title="`Question ${index + 1}${question.answer ? ' (Answered)' : ''}`"
      ></div>
    </div>
    
    <button 
      v-if="currentIndex < (questions.length - 1)" 
      @click="handleNext" 
      class="nav-btn next-btn"
      :disabled="!questions[currentIndex]?.answer"
      :class="{ 'disabled': !questions[currentIndex]?.answer }"
    >
      Next
      <i class="fa fa-chevron-right"></i>
    </button>
    
    <button 
      v-else
      @click="handleSubmit" 
      class="nav-btn submit-btn"
      :disabled="!allQuestionsAnswered"
      :class="{ 'disabled': !allQuestionsAnswered }"
    >
      Submit Quiz
      <i class="fa fa-check"></i>
    </button>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';

const props = defineProps({
  questions: {
    type: Array,
    required: true
  },
  currentIndex: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['previous', 'next', 'go-to-question', 'submit']);

const { allQuestionsAnswered } = useAwarenessActions();

const allAnswered = computed(() => allQuestionsAnswered(props.questions));

const handlePrevious = () => {
  if (props.currentIndex > 0) {
    emit('previous');
  }
};

const handleNext = () => {
  if (props.currentIndex < props.questions.length - 1 && props.questions[props.currentIndex]?.answer) {
    emit('next');
  }
};

const handleGoToQuestion = (index) => {
  emit('go-to-question', index);
};

const handleSubmit = () => {
  if (allAnswered.value) {
    emit('submit');
  }
};
</script>

<style scoped>
.quiz-navigation {
  background: #f8fafc;
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.prev-btn {
  background: #6b7280;
  color: white;
}

.prev-btn:hover:not(.disabled) {
  background: #4b5563;
}

.next-btn {
  background: #3b82f6;
  color: white;
}

.next-btn:hover:not(.disabled) {
  background: #2563eb;
}

.submit-btn {
  background: #10b981;
  color: white;
}

.submit-btn:hover:not(.disabled) {
  background: #059669;
}

.nav-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.nav-indicators {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d1d5db;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-dot:hover {
  transform: scale(1.2);
}

.nav-dot.active {
  background: #3b82f6;
  transform: scale(1.3);
}

.nav-dot.answered {
  background: #10b981;
}

.nav-dot.answered::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
}

.nav-dot.active.answered {
  background: #059669;
}

@media (max-width: 768px) {
  .quiz-navigation {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-btn {
    min-width: 100px;
    padding: 0.5rem 1rem;
  }
  
  .nav-indicators {
    order: -1;
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
