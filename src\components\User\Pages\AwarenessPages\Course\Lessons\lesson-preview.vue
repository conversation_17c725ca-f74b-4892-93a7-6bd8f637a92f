<template>
    <div class="modern-lesson-container" v-if="currentLesson != null">
        <div class="lesson-header">
            <div class="lesson-breadcrumb">
                <router-link :to="{ name: 'AwarenessCoursePreview', params: { course_id: $route.params.course_id } }" class="breadcrumb-link">
                    <i class="fa fa-arrow-left"></i>
                    Course Overview
                </router-link>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">{{ currentLesson.title }}</span>
            </div>

            <div class="lesson-status-bar">
                <div class="lesson-type-badge" :class="currentLesson.is_quiz === '1' ? 'quiz-badge' : 'lesson-badge'">
                    <i class="fa" :class="currentLesson.is_quiz === '1' ? 'fa-question-circle' : 'fa-play-circle'"></i>
                    <span>{{ currentLesson.is_quiz === '1' ? 'Quiz' : 'Lesson' }}</span>
                </div>

                <div class="completion-status" v-if="currentLesson.completed === 1">
                    <i class="fa fa-check-circle"></i>
                    <span>Completed</span>
                </div>
            </div>
        </div>

        <div v-if="currentLesson.is_quiz !== '1'" class="lesson-content-wrapper">
            <div class="lesson-content-card">
                <div class="lesson-title-section">
                    <h1 class="lesson-title">{{ currentLesson.title }}</h1>
                    <div class="lesson-meta">
                        <div class="meta-item">
                            <i class="fa fa-book"></i>
                            <span>Learning Material</span>
                        </div>
                        <div class="meta-item" v-if="currentLesson.completed === 1">
                            <i class="fa fa-check-circle completed-icon"></i>
                            <span>Completed</span>
                        </div>
                    </div>
                </div>

                <div class="lesson-content" v-if="currentLesson.is_quiz === '0'">
                    <div class="content-body ql-editor" v-html="currentLesson.description"></div>

                    <LessonActions
                        :lesson="currentLesson"
                        :course-id="courseId"
                        :topic-id="topicId"
                        :lesson-id="lessonId"
                        :loading="loading"
                        @mark-complete="handleMarkComplete"
                    />
                </div>
            </div>
        </div>

        <!-- Quiz Content -->
        <div v-else class="quiz-wrapper">
            <!-- Quiz Results View -->
            <div v-if="quiz_result != null" class="quiz-results">
                <div class="results-card">
                    <div class="results-header">
                        <div class="score-circle" :class="getScoreClass(quiz_result.score)">
                            <div class="score-value">{{ quiz_result.score }}%</div>
                            <div class="score-label">Score</div>
                        </div>
                        <div class="results-info">
                            <h2 class="results-title">{{ getScoreMessage(quiz_result.score) }}</h2>
                            <p class="results-description">{{ getScoreDescription(quiz_result.score) }}</p>
                        </div>
                    </div>
                    
                    <div class="answers-grid">
                        <h3 class="answers-title">Your Answers</h3>
                        <div class="answers-overview">
                            <div 
                                v-for="(answer, index) in quiz_result.submissions" 
                                :key="index"
                                class="answer-indicator"
                                :class="answer.correct === 1 ? 'correct' : 'incorrect'"
                                :title="`Question ${index + 1}: ${answer.correct === 1 ? 'Correct' : 'Incorrect'}`"
                            >
                                <span class="question-number">{{ index + 1 }}</span>
                                <i class="fa" :class="answer.correct === 1 ? 'fa-check' : 'fa-times'"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="quiz-actions">
                        <div v-if="lesson.is_final_exam === '0'" class="standard-quiz-actions">
                            <button @click="retakeQuiz" class="retake-btn">
                                <i class="fa fa-refresh"></i>
                                Retake Quiz
                            </button>
                        </div>
                        
                        <div v-else class="final-exam-actions">
                            <div v-if="quiz_result.score >= 80" class="passing-actions">
                                <div class="congratulations">
                                    <i class="fa fa-trophy"></i>
                                    <span>Congratulations! You passed the final exam.</span>
                                </div>
                                <button @click="getCertificate" class="certificate-btn">
                                    <i class="fa fa-certificate"></i>
                                    Download Certificate
                                </button>
                            </div>
                            
                            <div v-else class="failing-actions">
                                <div class="try-again-message">
                                    <i class="fa fa-info-circle"></i>
                                    <div class="message-content">
                                        <h4>Almost there!</h4>
                                        <p>You need to score at least 80% to get the certificate. Keep trying!</p>
                                    </div>
                                </div>
                                <button @click="retakeQuiz" class="retake-btn">
                                    <i class="fa fa-refresh"></i>
                                    Retake Final Exam
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quiz Taking View -->
            <div v-else class="quiz-taking">
                <div class="quiz-header">
                    <h1 class="quiz-title">{{ lesson.title }}</h1>
                    <div class="quiz-progress">
                        <div class="progress-info">
                            <span class="question-counter">
                                Question {{ current_index + 1 }} of {{ questions.length }}
                            </span>
                        </div>
                        <div class="progress-bar">
                            <div 
                                class="progress-fill" 
                                :style="{ width: `${((current_index + 1) / questions.length) * 100}%` }"
                            ></div>
                        </div>
                    </div>
                </div>

                <div v-if="questions.length > 0" class="quiz-content">
                    <div v-for="(question, index) in questions" :key="index" class="question-slide" v-show="current_index === index">
                        <div class="question-card">
                            <div class="question-header">
                                <div class="question-number-badge">{{ index + 1 }}</div>
                                <h2 class="question-text">{{ question.question }}</h2>
                            </div>
                            
                            <div class="options-container">
                                <div class="option-item" v-for="optionNum in 4" :key="optionNum">
                                    <label 
                                        :for="`option_${optionNum}_${index}`" 
                                        class="option-label"
                                        :class="{ 'selected': question.answer === optionNum.toString() }"
                                    >
                                        <input 
                                            class="option-input" 
                                            type="radio" 
                                            name="answer" 
                                            :value="optionNum" 
                                            :id="`option_${optionNum}_${index}`"
                                            @change="question.answer = optionNum.toString()" 
                                            :checked="question.answer === optionNum.toString()"
                                        >
                                        <div class="option-indicator">
                                            <span class="option-letter">{{ String.fromCharCode(64 + optionNum) }}</span>
                                        </div>
                                        <span class="option-text">{{ question[`option_${optionNum}`] }}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <QuizNavigation
                    :questions="questions"
                    :current-index="currentIndex"
                    @previous="handlePrevious"
                    @next="handleNext"
                    @go-to-question="handleGoToQuestion"
                    @submit="handleSubmitQuiz"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import LessonActions from '@/components/General/LessonActions.vue';
import QuizNavigation from '@/components/General/QuizNavigation.vue';

const route = useRoute();

const courseId = ref(null);
const topicId = ref(null);
const lessonId = ref(null);
const questions = ref([]);
const currentIndex = ref(0);
const quizResult = ref(null);

const emit = defineEmits(['currentLesson', 'refresh']);

const {
    loading,
    currentLesson,
    getSingleLesson,
    completeLesson,
    submitQuiz,
    downloadCourseCertificate,
    confirmMarkAsComplete,
    showLessonCompleted,
    showError,
    confirmSubmitQuiz,
    showIncompleteQuiz,
    showQuizSubmitted,
    confirmRetakeQuiz,
    showCertificateDownloaded,
    allQuestionsAnswered,
    getScoreClass,
    getScoreMessage,
    getScoreDescription
} = useAwarenessActions();

const loadLesson = async () => {
    try {
        courseId.value = route.params.course_id;
        topicId.value = route.params.topic_id;
        lessonId.value = route.params.lesson_id;

        const { data } = await getSingleLesson(courseId.value, topicId.value, lessonId.value);

        if (data.lesson.is_quiz === '1') {
            quizResult.value = data.quiz_result;
            questions.value = data.lesson.questions || [];
            currentIndex.value = 0;
        }

        emit('currentLesson', { topic_id: topicId.value, lesson_id: lessonId.value });
    } catch (error) {
        console.error('Error loading lesson:', error);
        await showError('Failed to load lesson.');
    }
};

const handleMarkComplete = async (response) => {
    await loadLesson();
    emit('refresh', { topic_id: topicId.value, lesson_id: lessonId.value });
};

const handlePrevious = () => {
    if (currentIndex.value > 0) {
        currentIndex.value--;
    }
};

const handleNext = () => {
    if (currentIndex.value < questions.value.length - 1) {
        currentIndex.value++;
    }
};

const handleGoToQuestion = (index) => {
    currentIndex.value = index;
};

const handleSubmitQuiz = async () => {
    try {
        const response = await submitQuiz(courseId.value, topicId.value, lessonId.value, questions.value);
        quizResult.value = response.data;
        emit('refresh', { topic_id: topicId.value, lesson_id: lessonId.value });
    } catch (error) {
        console.error('Error submitting quiz:', error);
    }
};

const handleRetakeQuiz = async () => {
    const result = await confirmRetakeQuiz();
    if (result.isConfirmed) {
        currentIndex.value = 0;
        quizResult.value = null;
        questions.value.forEach(q => {
            q.answer = null;
        });
    }
};

const handleGetCertificate = async () => {
    try {
        await downloadCourseCertificate(courseId.value);
        await showCertificateDownloaded();
    } catch (error) {
        await showError('Failed to download certificate.');
    }
};

watch(() => route.params.lesson_id, (newId, oldId) => {
    if (newId !== oldId) {
        loadLesson();
    }
});

watch(() => route.fullPath, (newPath, oldPath) => {
    if (newPath !== oldPath) {
        loadLesson();
    }
});

onMounted(async () => {
    await loadLesson();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/lesson-preview.scss';
</style>