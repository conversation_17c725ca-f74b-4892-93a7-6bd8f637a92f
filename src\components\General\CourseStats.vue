<template>
  <div class="course-stats">
    <div class="stat-item" v-for="stat in stats" :key="stat.label">
      <div class="stat-icon" :class="stat.iconClass">
        <i class="fa" :class="stat.icon"></i>
      </div>
      <div class="stat-content">
        <span class="stat-number">{{ stat.value }}</span>
        <span class="stat-label">{{ stat.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  totalCourses: {
    type: Number,
    default: 0
  },
  completedCourses: {
    type: Number,
    default: 0
  },
  totalCertificates: {
    type: Number,
    default: 0
  },
  inProgressCourses: {
    type: Number,
    default: 0
  },
  customStats: {
    type: Array,
    default: () => []
  },
  layout: {
    type: String,
    default: 'horizontal', // 'horizontal' or 'vertical'
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  }
});

const stats = computed(() => {
  const defaultStats = [
    {
      label: 'Started Courses',
      value: props.totalCourses,
      icon: 'fa-book',
      iconClass: 'stat-icon-blue'
    },
    {
      label: 'Completed',
      value: props.completedCourses,
      icon: 'fa-check-circle',
      iconClass: 'stat-icon-green'
    },
    {
      label: 'Certificates Available',
      value: props.totalCertificates,
      icon: 'fa-certificate',
      iconClass: 'stat-icon-purple'
    }
  ];

  if (props.inProgressCourses > 0) {
    defaultStats.splice(2, 0, {
      label: 'In Progress',
      value: props.inProgressCourses,
      icon: 'fa-play-circle',
      iconClass: 'stat-icon-orange'
    });
  }

  return props.customStats.length > 0 ? props.customStats : defaultStats;
});
</script>

<style scoped>
.course-stats {
  display: flex;
  gap: 2rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.course-stats.vertical {
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-width: 180px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.stat-icon-blue {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #3b82f6;
}

.stat-icon-green {
  background: linear-gradient(135deg, #dcfce7, #bbf7d0);
  color: #10b981;
}

.stat-icon-purple {
  background: linear-gradient(135deg, #ede9fe, #ddd6fe);
  color: #8b5cf6;
}

.stat-icon-orange {
  background: linear-gradient(135deg, #fed7aa, #fdba74);
  color: #f59e0b;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .course-stats {
    gap: 1rem;
  }
  
  .stat-item {
    min-width: 150px;
    padding: 1rem;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .stat-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .course-stats {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .stat-item {
    width: 100%;
    min-width: unset;
    justify-content: center;
  }
}

/* Vertical Layout */
.course-stats.vertical .stat-item {
  width: 100%;
  max-width: 300px;
}

/* Compact Layout */
.course-stats.compact .stat-item {
  padding: 1rem;
  min-width: 120px;
}

.course-stats.compact .stat-icon {
  width: 36px;
  height: 36px;
  font-size: 0.875rem;
}

.course-stats.compact .stat-number {
  font-size: 1.25rem;
}

.course-stats.compact .stat-label {
  font-size: 0.75rem;
}
</style>
