<!-- src/components/User/Pages/AwarenessPages/course-list.vue -->
<template>
    <div class="course-list-page">
        <PageHeader
            title="Available Courses"
            subtitle="Choose from our comprehensive collection of AI awareness training courses"
            icon="fa-book"
        >
            <template #actions>
                <router-link :to="{ name: 'AwarenessEvaluation' }" class="btn-secondary">
                    <i class="fa fa-arrow-left"></i> Back to Dashboard
                </router-link>
            </template>
        </PageHeader>

        <CourseStats
            :total-courses="courses.length"
            :custom-stats="customStats"
        />

        <LoadingSpinner v-if="loading" message="Preparing your learning experience..." />

        <!-- Empty State -->
        <div v-else-if="courses.length === 0" class="empty-state">
            <div class="empty-content">
                <div class="empty-illustration">
                    <div class="book-stack">
                        <div class="book book-1"></div>
                        <div class="book book-2"></div>
                        <div class="book book-3"></div>
                    </div>
                    <div class="floating-elements">
                        <div class="element element-1"></div>
                        <div class="element element-2"></div>
                        <div class="element element-3"></div>
                    </div>
                </div>
                <h2 class="empty-title">No Courses Available</h2>
                <p class="empty-description">
                    We're working on adding more courses to help you enhance your AI knowledge. 
                    Please check back soon!
                </p>
                <div class="empty-actions">
                    <router-link :to="{ name: 'AwarenessEvaluation' }" class="btn-primary"> <i class="fa fa-arrow-left"></i> Back to Dashboard </router-link>
                </div>
            </div>
        </div>

        <!-- Courses Grid -->
        <div v-else class="courses-section">
            <div class="section-header">
                <h2 class="section-title">Start Your Learning Journey</h2>
                <div class="view-options">
                    <button @click="viewMode = 'grid'" :class="['view-btn', { active: viewMode === 'grid' }]" > <i class="fa fa-th"></i> </button>
                    <button @click="viewMode = 'list'" :class="['view-btn', { active: viewMode === 'list' }]" > <i class="fa fa-list"></i> </button>
                </div>
            </div>

            <div :class="['courses-container', viewMode]">
                <CourseCard
                    v-for="(course, index) in filteredCourses"
                    :key="course._id"
                    :course="course"
                    :view-mode="viewMode"
                    :show-progress="false"
                    continue-button-text="Start Course"
                    route-name="AwarenessCoursePreview"
                    :style="{ animationDelay: `${index * 0.1}s` }"
                    @course-click="handleCourseClick"
                />
            </div>
        </div>

        <!-- Course Benefits Section -->
        <div class="benefits-section">
            <div class="benefits-content">
                <h2 class="benefits-title">Why Choose Our AI Training?</h2>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <div class="benefit-icon"> <i class="fa fa-graduation-cap"></i> </div>
                        <h3>Expert-Designed Curriculum</h3>
                        <p>Courses created by AI industry experts with real-world experience</p>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon"> <i class="fa fa-certificate"></i> </div>
                        <h3>Industry Certification</h3>
                        <p>Earn recognized certificates to showcase your AI knowledge</p>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon"> <i class="fa fa-clock-o"></i> </div>
                        <h3>Self-Paced Learning</h3>
                        <p>Learn at your own pace with lifetime access to course materials</p>
                    </div>
                    <div class="benefit-item">
                        <div class="benefit-icon"> <i class="fa fa-support"></i> </div>
                        <h3>24/7 Support</h3>
                        <p>Get help whenever you need it with our dedicated support team</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import PageHeader from '@/components/General/PageHeader.vue';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import CourseCard from '@/components/General/CourseCard.vue';
import CourseStats from '@/components/General/CourseStats.vue';

const viewMode = ref('grid');

const {
    loading,
    courses,
    searchQuery,
    filteredCourses,
    getAvailableCourses,
    startCourse,
    showError
} = useAwarenessActions();

const customStats = computed(() => [
    {
        label: 'Available Courses',
        value: courses.value.length,
        icon: 'fa-book',
        iconClass: 'stat-icon-blue'
    },
    {
        label: 'Learning Mode',
        value: 'Self-paced',
        icon: 'fa-clock-o',
        iconClass: 'stat-icon-orange'
    },
    {
        label: 'Certification',
        value: 'Free',
        icon: 'fa-certificate',
        iconClass: 'stat-icon-purple'
    },
    {
        label: 'Level Training',
        value: 'Expert',
        icon: 'fa-graduation-cap',
        iconClass: 'stat-icon-green'
    }
]);

const handleCourseClick = (course) => {
    startCourse(course);
};

const loadCourses = async () => {
    try {
        await getAvailableCourses();
    } catch (error) {
        console.error('Error loading courses:', error);
        await showError('Failed to load courses.');
    }
};

onMounted(async () => {
    await loadCourses();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/course-list.scss';
</style>