<template>
  <div class="progress-container">
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        :style="{ width: `${completionPercentage}%` }"
        :class="getProgressClass(completionPercentage)"
      ></div>
    </div>
    <span class="progress-text" v-if="showText">
      {{ completedLessons }} / {{ totalLessons }} lessons completed
    </span>
    <span class="progress-percentage" v-if="showPercentage">
      {{ Math.round(completionPercentage) }}%
    </span>
  </div>
</template>

<script setup>
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';

const props = defineProps({
  completionPercentage: {
    type: Number,
    default: 0
  },
  completedLessons: {
    type: Number,
    default: 0
  },
  totalLessons: {
    type: Number,
    default: 0
  },
  showText: {
    type: Boolean,
    default: true
  },
  showPercentage: {
    type: Boolean,
    default: false
  },
  height: {
    type: String,
    default: '8px'
  },
  borderRadius: {
    type: String,
    default: '4px'
  }
});

const { getProgressClass } = useAwarenessActions();
</script>

<style scoped>
.progress-container {
  margin: 1rem 0;
}

.progress-bar {
  width: 100%;
  height: v-bind(height);
  background: #f3f4f6;
  border-radius: v-bind(borderRadius);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: v-bind(borderRadius);
}

.progress-fill.progress-complete {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-fill.progress-good {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.progress-fill.progress-started {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.progress-fill.progress-none {
  background: #e5e7eb;
}

.progress-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
}

.progress-percentage {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 600;
  text-align: center;
}
</style>
