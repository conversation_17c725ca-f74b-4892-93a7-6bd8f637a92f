// src/composables/consultancy/useAdminConsultancyActions.js
import { ref } from 'vue';
import ApiService from '@/services/ApiService';
import { ConsultancyApiRoutes } from '@/ApiRoutes/consultancyApiRoutes';

export const useAdminConsultancyActions = () => {
    const loading = ref(false);
    const error = ref(null);
    const success = ref(null);

    // Slots management
    const slots = ref([]);
    const slotsLoading = ref(false);

    // Bookings management
    const bookings = ref([]);
    const bookingsLoading = ref(false);

    // Statistics
    const stats = ref({});
    const statsLoading = ref(false);

    // Get all slots
    const getSlots = async (filters = {}) => {
        slotsLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.GetAllSlots,
                filters
            );

            if (response.status === 200) {
                slots.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch slots';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            slotsLoading.value = false;
        }
    };

    // Create slot
    const createSlot = async (slotData) => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.CreateSlot,
                slotData
            );

            if (response.status === 200) {
                success.value = 'Slot created successfully!';
                await getSlots(); // Refresh slots
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to create slot';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Update slot
    const updateSlot = async (slotData) => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.UpdateSlot,
                slotData
            );

            if (response.status === 200) {
                success.value = 'Slot updated successfully!';
                await getSlots(); // Refresh slots
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to update slot';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Delete slot
    const deleteSlot = async (slotId) => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.DELETE(
                ConsultancyApiRoutes.Admin.DeleteSlot(slotId)
            );

            if (response.status === 200) {
                success.value = 'Slot deleted successfully!';
                await getSlots(); // Refresh slots
                return { success: true };
            } else {
                error.value = response.error || 'Failed to delete slot';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Get all bookings
    const getBookings = async (filters = {}) => {
        bookingsLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.GetAllBookings,
                filters
            );

            if (response.status === 200) {
                bookings.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch bookings';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            bookingsLoading.value = false;
        }
    };

    // Update meeting link
    const updateMeetingLink = async (bookingId, meetingLink) => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.UpdateMeetingLink,
                { booking_id: bookingId, meeting_link: meetingLink }
            );

            if (response.status === 200) {
                success.value = 'Meeting link updated successfully!';
                await getBookings(); // Refresh bookings
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to update meeting link';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Send meeting link
    const sendMeetingLink = async (bookingId) => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.SendMeetingLink,
                { booking_id: bookingId }
            );

            if (response.status === 200) {
                success.value = 'Meeting link sent successfully!';
                return { success: true };
            } else {
                error.value = response.error || 'Failed to send meeting link';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Get statistics
    const getStats = async () => {
        statsLoading.value = true;
        error.value = null;

        try {
            const response = await ApiService.GET(ConsultancyApiRoutes.Admin.GetStats);

            if (response.status === 200) {
                stats.value = response.data;
                return { success: true, data: response.data };
            } else {
                error.value = response.error || 'Failed to fetch statistics';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            statsLoading.value = false;
        }
    };

    // Cancel booking
    const cancelBooking = async (bookingId, reason = '') => {
        loading.value = true;
        error.value = null;
        success.value = null;

        try {
            const response = await ApiService.POST(
                ConsultancyApiRoutes.Admin.CancelBooking,
                { booking_id: bookingId, cancellation_reason: reason }
            );

            if (response.status === 200) {
                success.value = 'Booking cancelled successfully!';
                await getBookings(); // Refresh bookings
                return { success: true };
            } else {
                error.value = response.error || 'Failed to cancel booking';
                return { success: false, error: error.value };
            }
        } catch (err) {
            error.value = 'Network error occurred';
            return { success: false, error: error.value };
        } finally {
            loading.value = false;
        }
    };

    // Clear messages
    const clearMessages = () => {
        error.value = null;
        success.value = null;
    };

    return {
        // State
        loading,
        error,
        success,
        slots,
        slotsLoading,
        bookings,
        bookingsLoading,
        stats,
        statsLoading,

        // Actions
        getSlots,
        createSlot,
        updateSlot,
        deleteSlot,
        getBookings,
        updateMeetingLink,
        sendMeetingLink,
        getStats,
        cancelBooking,
        clearMessages,
    };
};
