.modern-lesson-container {
    max-width: 900px;
    margin: 0 auto;
    background: #f8fafc;
    min-height: 100vh;
}

/* <PERSON><PERSON> Header */
.lesson-header {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.lesson-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: #6b7280;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #059669;
}

.breadcrumb-separator {
    color: #9ca3af;
    margin: 0 0.25rem;
}

.breadcrumb-current {
    color: #1f2937;
    font-weight: 500;
}

.lesson-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.lesson-type-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.lesson-badge {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(29, 78, 216, 0.1));
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.quiz-badge {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.completion-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #059669;
    font-size: 0.875rem;
    font-weight: 600;
}

/* Regular Lesson Content */
.lesson-content-wrapper {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.lesson-content-card {
    padding: 2.5rem;
}

.lesson-title-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.lesson-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.lesson-meta {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.completed-icon {
    color: #059669;
}

.lesson-content {
    line-height: 1.7;
}

.content-body {
    font-size: 1.1rem;
    color: #374151;
    margin-bottom: 2rem;
}

.content-body h1, .content-body h2, .content-body h3, .content-body h4, .content-body h5, .content-body h6 {
    color: #1f2937;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.content-body p {
    margin-bottom: 1rem;
}

.content-body ul, .content-body ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-body li {
    margin-bottom: 0.5rem;
}

.content-body blockquote {
    border-left: 4px solid #059669;
    background: #f0fdf4;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 8px 8px 0;
}

.content-body code {
    background: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875em;
}

.content-body pre {
    background: #1f2937;
    color: #e5e7eb;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1rem 0;
}

.lesson-actions {
    display: flex;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.complete-lesson-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.complete-lesson-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.completion-message {
    display: flex;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.completion-card {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-radius: 16px;
    border: 1px solid #a7f3d0;
}

.completion-icon {
    font-size: 3rem;
    color: #059669;
    margin-bottom: 1rem;
}

.completion-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.completion-card p {
    color: #047857;
    margin: 0;
}

/* Quiz Results */
.quiz-results {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.results-card {
    padding: 2.5rem;
}

.results-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.score-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 4px solid;
    flex-shrink: 0;
}

.score-circle.excellent {
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-color: #059669;
    color: #059669;
}

.score-circle.good {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    border-color: #3b82f6;
    color: #3b82f6;
}

.score-circle.average {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border-color: #f59e0b;
    color: #d97706;
}

.score-circle.needs-improvement {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border-color: #ef4444;
    color: #dc2626;
}

.score-value {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.score-label {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
}

.results-info {
    flex: 1;
}

.results-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.results-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
}

.answers-grid {
    margin-bottom: 2rem;
}

.answers-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.answers-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.answer-indicator {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 0.25rem;
}

.answer-indicator.correct {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.answer-indicator.incorrect {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.answer-indicator:hover {
    transform: scale(1.05);
}

.question-number {
    font-size: 0.75rem;
    line-height: 1;
}

.answer-indicator i {
    font-size: 0.875rem;
}

.quiz-actions {
    border-top: 1px solid #e5e7eb;
    padding-top: 2rem;
}

.standard-quiz-actions,
.final-exam-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.final-exam-actions {
    flex-direction: column;
    gap: 1.5rem;
}

.passing-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.congratulations {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #059669;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #a7f3d0;
}

.congratulations i {
    font-size: 1.5rem;
    color: #fbbf24;
}

.failing-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.try-again-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #f59e0b;
    text-align: left;
}

.try-again-message i {
    font-size: 1.5rem;
    color: #d97706;
    flex-shrink: 0;
}

.message-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #92400e;
    margin-bottom: 0.5rem;
}

.message-content p {
    color: #b45309;
    margin: 0;
}

.retake-btn,
.certificate-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.retake-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.certificate-btn {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.certificate-btn:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
}

/* Quiz Taking */
.quiz-taking {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.quiz-header {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.quiz-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.quiz-progress {
    max-width: 400px;
    margin: 0 auto;
}

.progress-info {
    display: flex;
    justify-content: center;
    margin-bottom: 0.75rem;
}

.question-counter {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #059669, #047857);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.quiz-content {
    padding: 2rem;
}

.question-slide {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.question-card {
    max-width: 700px;
    margin: 0 auto;
}

.question-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.question-number-badge {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.125rem;
    flex-shrink: 0;
}

.question-text {
    flex: 1;
    font-size: 1.375rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
    margin: 0;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-item {
    width: 100%;
}

.option-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.option-label:hover {
    border-color: #059669;
    background: #f0fdf4;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-label.selected {
    border-color: #059669;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    box-shadow: 0 4px 20px rgba(5, 150, 105, 0.2);
}

.option-input {
    display: none;
}

.option-indicator {
    width: 36px;
    height: 36px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.option-label.selected .option-indicator {
    border-color: #059669;
    background: #059669;
    color: white;
}

.option-letter {
    font-weight: 700;
    font-size: 0.875rem;
}

.option-text {
    flex: 1;
    font-size: 1rem;
    color: #374151;
    line-height: 1.5;
}

.option-label.selected .option-text {
    color: #065f46;
    font-weight: 500;
}

/* Quiz Navigation */
.quiz-navigation {
    background: #f8fafc;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.prev-btn {
    background: #6b7280;
    color: white;
}

.prev-btn:hover:not(.disabled) {
    background: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.next-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
}

.next-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.submit-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.submit-btn:hover:not(.disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.nav-indicators {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #d1d5db;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-dot.active {
    background: #059669;
    transform: scale(1.2);
}

.nav-dot.answered {
    background: #3b82f6;
}

.nav-dot:hover {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-lesson-container {
        padding: 0;
    }
    
    .lesson-header {
        border-radius: 0;
        margin-bottom: 1rem;
    }
    
    .lesson-status-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .lesson-content-card {
        padding: 1.5rem;
    }
    
    .lesson-title {
        font-size: 1.5rem;
    }
    
    .lesson-meta {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .results-card {
        padding: 1.5rem;
    }
    
    .results-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .score-circle {
        width: 100px;
        height: 100px;
    }
    
    .score-value {
        font-size: 1.75rem;
    }
    
    .results-title {
        font-size: 1.5rem;
    }
    
    .answers-overview {
        justify-content: center;
    }
    
    .answer-indicator {
        width: 50px;
        height: 50px;
    }
    
    .quiz-header {
        padding: 1.5rem;
    }
    
    .quiz-title {
        font-size: 1.5rem;
    }
    
    .quiz-content {
        padding: 1.5rem;
    }
    
    .question-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .question-number-badge {
        width: 32px;
        height: 32px;
        font-size: 1rem;
        align-self: center;
    }
    
    .question-text {
        font-size: 1.125rem;
    }
    
    .option-label {
        padding: 1rem;
        gap: 0.75rem;
    }
    
    .option-indicator {
        width: 28px;
        height: 28px;
    }
    
    .option-letter {
        font-size: 0.75rem;
    }
    
    .option-text {
        font-size: 0.875rem;
    }
    
    .quiz-navigation {
        padding: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .nav-btn {
        min-width: 100px;
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }
    
    .nav-indicators {
        order: -1;
        width: 100%;
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .try-again-message {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .congratulations {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .lesson-header {
        padding: 1rem;
    }
    
    .lesson-breadcrumb {
        font-size: 0.75rem;
    }
    
    .lesson-type-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .completion-status {
        font-size: 0.75rem;
    }
    
    .lesson-content-card {
        padding: 1rem;
    }
    
    .lesson-title {
        font-size: 1.25rem;
    }
    
    .content-body {
        font-size: 1rem;
    }
    
    .results-card {
        padding: 1rem;
    }
    
    .score-circle {
        width: 80px;
        height: 80px;
    }
    
    .score-value {
        font-size: 1.5rem;
    }
    
    .results-title {
        font-size: 1.25rem;
    }
    
    .answers-title {
        font-size: 1.125rem;
    }
    
    .answer-indicator {
        width: 40px;
        height: 40px;
        font-size: 0.75rem;
    }
    
    .quiz-header {
        padding: 1rem;
    }
    
    .quiz-title {
        font-size: 1.25rem;
    }
    
    .quiz-content {
        padding: 1rem;
    }
    
    .question-text {
        font-size: 1rem;
    }
    
    .option-label {
        padding: 0.75rem;
    }
    
    .quiz-navigation {
        padding: 0.75rem;
    }
    
    .nav-btn {
        min-width: 80px;
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }
    
    .nav-dot {
        width: 10px;
        height: 10px;
    }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.lesson-header {
    animation: fadeInUp 0.6s ease-out;
}

.lesson-content-wrapper,
.quiz-results,
.quiz-taking {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

.completion-card {
    animation: bounceIn 0.8s ease-out;
}

.score-circle {
    animation: bounceIn 0.8s ease-out 0.3s both;
}

.answer-indicator {
    animation: fadeInUp 0.4s ease-out;
}

.answer-indicator:nth-child(1) { animation-delay: 0.1s; }
.answer-indicator:nth-child(2) { animation-delay: 0.15s; }
.answer-indicator:nth-child(3) { animation-delay: 0.2s; }
.answer-indicator:nth-child(4) { animation-delay: 0.25s; }
.answer-indicator:nth-child(5) { animation-delay: 0.3s; }

/* Focus States for Accessibility */
.breadcrumb-link:focus,
.complete-lesson-btn:focus,
.retake-btn:focus,
.certificate-btn:focus,
.nav-btn:focus,
.option-label:focus-within {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

.nav-dot:focus {
    outline: 2px solid #059669;
    outline-offset: 1px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .lesson-header,
    .lesson-content-wrapper,
    .quiz-results,
    .quiz-taking,
    .option-label {
        border-width: 2px;
    }
    
    .score-circle,
    .answer-indicator,
    .option-indicator {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .lesson-header,
    .lesson-content-wrapper,
    .quiz-results,
    .quiz-taking,
    .completion-card,
    .score-circle,
    .answer-indicator,
    .question-slide {
        animation: none;
    }
    
    .option-label,
    .nav-btn,
    .answer-indicator,
    .nav-dot,
    .progress-fill {
        transition: none;
    }
}

/* Print Styles */
@media print {
    .lesson-header,
    .lesson-content-wrapper,
    .quiz-results {
        background: white !important;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .quiz-navigation {
        display: none;
    }
    
    .complete-lesson-btn,
    .retake-btn,
    .certificate-btn,
    .nav-btn {
        background: white !important;
        color: black !important;
        border: 1px solid black;
    }
    
    .score-circle {
        border-color: black !important;
        color: black !important;
    }
}