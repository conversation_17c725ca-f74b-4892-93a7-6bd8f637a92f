// src/plugins/toast.js
// Simple toast notification system for better user feedback

const Toast = {
    install(app) {
        const toast = {
            success(message, duration = 3000) {
                this.show(message, 'success', duration);
            },
            error(message, duration = 5000) {
                this.show(message, 'error', duration);
            },
            info(message, duration = 3000) {
                this.show(message, 'info', duration);
            },
            warning(message, duration = 4000) {
                this.show(message, 'warning', duration);
            },
            show(message, type = 'info', duration = 3000) {
                // Create toast element
                const toastId = 'toast-' + Date.now();
                const toast = document.createElement('div');
                toast.id = toastId;
                toast.className = `toast-notification toast-${type}`;
                toast.innerHTML = `
                    <div class="toast-content">
                        <i class="toast-icon fa ${this.getIcon(type)}"></i>
                        <span class="toast-message">${message}</span>
                        <button class="toast-close" onclick="document.getElementById('${toastId}').remove()">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                `;

                // Add to container
                let container = document.getElementById('toast-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'toast-container';
                    container.className = 'toast-container';
                    document.body.appendChild(container);
                }

                container.appendChild(toast);

                // Auto remove
                setTimeout(() => {
                    if (document.getElementById(toastId)) {
                        document.getElementById(toastId).remove();
                    }
                }, duration);

                // Add CSS if not already added
                this.addStyles();
            },
            getIcon(type) {
                const icons = {
                    success: 'fa-check-circle',
                    error: 'fa-exclamation-circle',
                    warning: 'fa-exclamation-triangle',
                    info: 'fa-info-circle'
                };
                return icons[type] || icons.info;
            },
            addStyles() {
                if (document.getElementById('toast-styles')) return;

                const styles = document.createElement('style');
                styles.id = 'toast-styles';
                styles.textContent = `
                    .toast-container {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 9999;
                        pointer-events: none;
                    }
                    
                    .toast-notification {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                        margin-bottom: 10px;
                        min-width: 300px;
                        pointer-events: all;
                        animation: slideIn 0.3s ease-out;
                        border-left: 4px solid;
                    }
                    
                    .toast-success { border-left-color: #28a745; }
                    .toast-error { border-left-color: #dc3545; }
                    .toast-warning { border-left-color: #ffc107; }
                    .toast-info { border-left-color: #17a2b8; }
                    
                    .toast-content {
                        display: flex;
                        align-items: center;
                        padding: 12px 16px;
                    }
                    
                    .toast-icon {
                        margin-right: 12px;
                        font-size: 18px;
                    }
                    
                    .toast-success .toast-icon { color: #28a745; }
                    .toast-error .toast-icon { color: #dc3545; }
                    .toast-warning .toast-icon { color: #ffc107; }
                    .toast-info .toast-icon { color: #17a2b8; }
                    
                    .toast-message {
                        flex: 1;
                        font-size: 14px;
                        color: #333;
                    }
                    
                    .toast-close {
                        background: none;
                        border: none;
                        color: #999;
                        cursor: pointer;
                        font-size: 12px;
                        margin-left: 12px;
                        padding: 0;
                    }
                    
                    .toast-close:hover {
                        color: #666;
                    }
                    
                    @keyframes slideIn {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                    
                    @media (max-width: 480px) {
                        .toast-container {
                            left: 10px;
                            right: 10px;
                            top: 10px;
                        }
                        
                        .toast-notification {
                            min-width: auto;
                        }
                    }
                `;
                document.head.appendChild(styles);
            }
        };

        app.config.globalProperties.$toast = toast;
        app.provide('toast', toast);
    }
};

export default Toast;

// Usage in main.js (if using Vue 3):
// import Toast from './plugins/toast.js'
// app.use(Toast)

// Or for Vue 2, add this to your main.js:
// import Toast from './plugins/toast.js'
// Vue.use(Toast)