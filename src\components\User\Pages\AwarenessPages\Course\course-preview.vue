<template>
    <div class="modern-course-container">
        <div class="course-layout" v-if="currentCourse != null">
            <div class="course-header">
                <div class="header-content">
                    <div class="course-breadcrumb">
                        <router-link :to="{ name: 'CourseList' }" class="breadcrumb-link">
                            <i class="fa fa-arrow-left"></i> All Courses
                        </router-link>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">{{ currentCourse.title }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Main Course Content -->
            <div class="course-main-content">
                <!-- Sidebar - Topics Navigation -->
                <aside class="course-sidebar">
                    <div class="sidebar-header">
                        <h3 class="sidebar-title"> <i class="fa fa-list-ul"></i> Course Content </h3>
                        <div class="progress-overview">
                            <div class="progress-circle">
                                <svg viewBox="0 0 36 36" class="circular-chart">
                                    <path class="circle-bg"
                                        d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"
                                    />
                                    <path class="circle-progress"
                                        :stroke-dasharray="`${getOverallProgress(topics)}, 100`"
                                        d="M18 2.0845
                                        a 15.9155 15.9155 0 0 1 0 31.831
                                        a 15.9155 15.9155 0 0 1 0 -31.831"
                                    />
                                </svg>
                                <div class="progress-text">{{ Math.round(getOverallProgress(topics)) }}%</div>
                            </div>
                        </div>
                    </div>

                    <div class="topics-container">
                        <div class="topic-accordion">
                            <div v-for="(topic, tIndex) in topics" :key="topic._id" class="topic-item">
                                <!-- Topic Header -->
                                <div 
                                    class="topic-header"
                                    :class="{ 'active': topic_id === topic._id }"
                                    @click="toggleTopic(topic._id)"
                                >
                                    <div class="topic-info">
                                        <div class="topic-icon">
                                            <i class="fa fa-folder-o" v-if="topic_id !== topic._id"></i>
                                            <i class="fa fa-folder-open-o" v-else></i>
                                        </div>
                                        <div class="topic-details">
                                            <h4 class="topic-title">{{ topic.title }}</h4>
                                            <span class="topic-lessons-count">{{ topic.lessons.length }} lessons</span>
                                        </div>
                                    </div>
                                    <div class="topic-actions">
                                        <div class="topic-progress">
                                            <span class="progress-fraction">{{ getTopicProgress(topic) }}</span>
                                        </div>
                                        <i class="fa fa-chevron-down expand-icon" :class="{ 'rotated': topic_id === topic._id }"></i>
                                    </div>
                                </div>

                                <!-- Topic Lessons -->
                                <div class="topic-lessons" :class="{ 'expanded': topic_id === topic._id }">
                                    <div v-if="topic.lessons.length === 0" class="no-lessons">
                                        <i class="fa fa-info-circle"></i>
                                        <span>No lessons available</span>
                                    </div>
                                    
                                    <div v-else class="lessons-list">
                                        <div 
                                            v-for="(lesson, lIndex) in topic.lessons" 
                                            :key="lesson._id"
                                            class="lesson-item"
                                            :class="{ 
                                                'current': lesson_id === lesson._id,
                                                'completed': lesson.completed === 1,
                                                'locked': lesson.readable === 0
                                            }"
                                        >
                                            <router-link 
                                                v-if="lesson.readable === 1" 
                                                :to="{name: 'LessonPreview', params:{course_id: course._id, topic_id: topic._id, lesson_id: lesson._id}}"
                                                class="lesson-link"
                                            >
                                                <div class="lesson-status">
                                                    <i class="fa fa-circle-o" v-if="lesson.completed === 0"></i>
                                                    <i class="fa fa-check-circle" v-else></i>
                                                </div>
                                                <div class="lesson-content">
                                                    <span class="lesson-title">{{ lesson.title }}</span>
                                                    <div class="lesson-meta">
                                                        <span class="lesson-type" v-if="lesson.is_quiz === '1'">
                                                            <i class="fa fa-question-circle"></i>
                                                            Quiz
                                                        </span>
                                                        <span class="lesson-type" v-else>
                                                            <i class="fa fa-play-circle"></i>
                                                            Lesson
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="lesson-indicator">
                                                    <i class="fa fa-play" v-if="lesson_id === lesson._id"></i>
                                                </div>
                                            </router-link>
                                            
                                            <div v-else class="lesson-locked">
                                                <div class="lesson-status">
                                                    <i class="fa fa-lock"></i>
                                                </div>
                                                <div class="lesson-content">
                                                    <span class="lesson-title">{{ lesson.title }}</span>
                                                    <div class="lesson-meta">
                                                        <span class="lesson-type locked">
                                                            <i class="fa fa-lock"></i>
                                                            Locked
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- Main Content Area -->
                <main class="course-content">
                    <div v-if="currentPage === 'AwarenessCoursePreview'" class="course-overview">
                        <div class="overview-card">
                            <div class="overview-header">
                                <div class="course-info">
                                    <h2 class="overview-title">{{ currentCourse.title }}</h2>
                                    <p class="overview-description">{{ currentCourse.description }}</p>

                                    <CourseStats
                                        :custom-stats="courseOverviewStats"
                                        layout="horizontal"
                                    />
                                </div>
                            </div>

                            <div class="getting-started" v-if="getOverallProgress(topics) === 0">
                                <h3 class="getting-started-title">
                                    <i class="fa fa-rocket"></i>
                                    Ready to start learning?
                                </h3>
                                <p class="getting-started-text">
                                    Begin your journey by selecting the first topic from the sidebar and complete lessons to earn your certificate.
                                </p>
                                <button @click="handleStartFirstLesson" class="start-learning-btn" v-if="getFirstAvailableLesson(topics)">
                                    <i class="fa fa-play"></i>
                                    Start Learning
                                </button>
                            </div>

                            <div class="continue-learning" v-else-if="getOverallProgress(topics) < 100">
                                <h3 class="continue-title">
                                    <i class="fa fa-play-circle"></i>
                                    Continue Learning
                                </h3>
                                <p class="continue-text">
                                    You've completed {{ getCompletedLessons(topics) }} out of {{ getTotalLessons(topics) }} lessons. Keep going!
                                </p>
                                <button @click="handleContinueFromLastLesson" class="continue-btn">
                                    <i class="fa fa-play"></i>
                                    Continue Course
                                </button>
                            </div>

                            <div class="course-completed" v-else>
                                <h3 class="completed-title">
                                    <i class="fa fa-trophy"></i>
                                    Congratulations!
                                </h3>
                                <p class="completed-text">
                                    You've successfully completed this course. Your certificate is ready for download.
                                </p>
                                <button @click="handleDownloadCertificate" class="certificate-btn">
                                    <i class="fa fa-certificate"></i>
                                    Download Certificate
                                </button>
                            </div>
                        </div>
                    </div>
    
                    <!-- Lesson Content -->
                    <div v-else class="lesson-content"> <router-view @refresh="restartPage()" @currentLesson="getCurrentLesson"/> </div>
                </main>
            </div>
        </div>

        <LoadingSpinner v-else message="Preparing your learning experience..." />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import CourseStats from '@/components/General/CourseStats.vue';

const route = useRoute();
const router = useRouter();

const currentPage = ref('AwarenessCoursePreview');
const courseId = ref(null);
const topicId = ref(null);
const lessonId = ref(null);

const {
    loading,
    currentCourse,
    topics,
    getSingleCourse,
    getCourseTopics,
    getOverallProgress,
    getTotalLessons,
    getCompletedLessons,
    getTopicProgress,
    getFirstAvailableLesson,
    startFirstLesson,
    continueFromLastLesson,
    downloadCourseCertificate,
    showCertificateDownloaded,
    showError
} = useAwarenessActions();

const courseOverviewStats = computed(() => [
    {
        label: 'Topics',
        value: topics.value.length,
        icon: 'fa-folder',
        iconClass: 'stat-icon-blue'
    },
    {
        label: 'Lessons',
        value: getTotalLessons(topics.value),
        icon: 'fa-play-circle',
        iconClass: 'stat-icon-green'
    },
    {
        label: 'Completed',
        value: getCompletedLessons(topics.value),
        icon: 'fa-check-circle',
        iconClass: 'stat-icon-purple'
    }
]);

const toggleTopic = (topicIdToToggle) => {
    topicId.value = topicId.value === topicIdToToggle ? null : topicIdToToggle;
};

const getCurrentLesson = (payload) => {
    topicId.value = payload.topic_id;
    lessonId.value = payload.lesson_id;
};

const handleStartFirstLesson = () => {
    startFirstLesson(currentCourse.value, topics.value);
};

const handleContinueFromLastLesson = () => {
    continueFromLastLesson(currentCourse.value, topics.value);
};

const handleDownloadCertificate = async () => {
    try {
        await downloadCourseCertificate(courseId.value);
        await showCertificateDownloaded();
    } catch (error) {
        await showError('Failed to download certificate.');
    }
};

const loadCourseData = async () => {
    try {
        courseId.value = route.params.course_id;
        currentPage.value = route.name;

        await getSingleCourse(courseId.value);
        await getCourseTopics(courseId.value);
    } catch (error) {
        console.error('Error loading course data:', error);
        await showError('Failed to load course data.');
    }
};

watch(() => route.params.course_id, (newId, oldId) => {
    if (newId !== oldId) {
        loadCourseData();
    }
});

watch(() => route.fullPath, (newPath, oldPath) => {
    if (newPath !== oldPath) {
        loadCourseData();
    }
});

onMounted(async () => {
    window.scrollTo(0, 0);
    await loadCourseData();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/AwarenessPages/course-preview.scss';
</style>