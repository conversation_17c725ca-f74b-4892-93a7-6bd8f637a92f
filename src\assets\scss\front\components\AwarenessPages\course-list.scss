.course-list-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Stats Section */
.stats-section {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    line-height: 1;
}

.stat-label {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Loading Section */
.loading-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.loading-container {
    text-align: center;
}

.loading-spinner {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    margin-bottom: 2rem;
}

.spinner-ring {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 8px solid #3b82f6;
    border-radius: 50%;
    animation: ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #3b82f6 transparent transparent transparent;
}

.spinner-ring:nth-child(1) { animation-delay: -0.45s; }
.spinner-ring:nth-child(2) { animation-delay: -0.3s; }
.spinner-ring:nth-child(3) { animation-delay: -0.15s; }

@keyframes ring {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.loading-text {
    color: #6b7280;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
}

.empty-illustration {
    position: relative;
    margin-bottom: 2rem;
    display: inline-block;
}

.book-stack {
    position: relative;
    width: 120px;
    height: 100px;
}

.book {
    position: absolute;
    width: 80px;
    height: 12px;
    border-radius: 2px;
}

.book-1 {
    background: #3b82f6;
    bottom: 0;
    left: 20px;
    transform: rotate(-5deg);
}

.book-2 {
    background: #059669;
    bottom: 12px;
    left: 15px;
    transform: rotate(2deg);
}

.book-3 {
    background: #f59e0b;
    bottom: 24px;
    left: 25px;
    transform: rotate(-3deg);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    opacity: 0.1;
    animation: float 3s ease-in-out infinite;
}

.element-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation-delay: -1s;
}

.element-2 {
    width: 16px;
    height: 16px;
    top: 60%;
    right: 20%;
    animation-delay: -2s;
}

.element-3 {
    width: 12px;
    height: 12px;
    bottom: 20%;
    left: 30%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.empty-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto 2.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

/* Courses Section */
.courses-section {
    margin-bottom: 4rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
}

.section-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.view-options {
    display: flex;
    gap: 0.5rem;
    background: #f3f4f6;
    padding: 0.25rem;
    border-radius: 8px;
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.view-btn.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.courses-container.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
}

.courses-container.list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Course Cards */
.course-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.course-card.list-view {
    display: flex;
    align-items: center;
    padding: 1.5rem;
}

.course-card.list-view .course-image {
    width: 120px;
    height: 80px;
    flex-shrink: 0;
    margin-right: 2rem;
}

.course-card.list-view .course-content {
    flex: 1;
    padding: 0;
}

.course-card.list-view .course-footer {
    padding: 0;
    margin-left: 2rem;
    flex-shrink: 0;
}

.course-image {
    width: 100%;
    height: 220px;
    position: relative;
    overflow: hidden;
}

.course-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-img {
    transform: scale(1.05);
}

.course-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #9ca3af;
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, transparent 50%);
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    padding: 1rem;
}

.level-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #1f2937;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.course-content {
    padding: 1.5rem;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-category {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.course-duration {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.course-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.feature i {
    color: #059669;
    width: 16px;
}

.course-footer {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.info-item i {
    width: 14px;
    color: #f59e0b;
}

.start-course-btn {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.start-course-btn:hover {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

.card-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.course-card:hover .card-shine {
    left: 100%;
}

/* Benefits Section */
.benefits-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 24px;
    padding: 4rem 2rem;
    margin-top: 4rem;
}

.benefits-content {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.benefits-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 3rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.benefit-item {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1.5rem;
}

.benefit-item h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.benefit-item p {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .courses-container.grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .course-list-page {
        padding: 0 0.5rem;
    }

    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .page-title {
        font-size: 1.875rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .courses-container.grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .course-card.list-view {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
    }

    .course-card.list-view .course-image {
        width: 100%;
        height: 180px;
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .course-card.list-view .course-footer {
        margin-left: 0;
        margin-top: 1rem;
    }

    .benefits-section {
        padding: 3rem 1.5rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }

    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .course-content {
        padding: 1rem;
    }

    .course-footer {
        padding: 0 1rem 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .start-course-btn {
        justify-content: center;
    }

    .benefits-section {
        padding: 2rem 1rem;
    }

    .benefits-title {
        font-size: 1.5rem;
    }

    .benefit-item {
        padding: 1.5rem;
    }
}

/* Focus states for accessibility */
.start-course-btn:focus,
.btn-primary:focus,
.btn-secondary:focus,
.view-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .course-card {
        border-width: 2px;
    }

    .stat-card {
        border-width: 2px;
    }

    .benefit-item {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .course-card,
    .stat-card,
    .benefit-item,
    .start-course-btn,
    .btn-primary,
    .btn-secondary {
        transition: none;
    }

    .card-shine {
        display: none;
    }

    .course-img {
        transition: none;
    }

    @keyframes slideInUp {
        from, to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes float {
        from, to {
            transform: translateY(0);
        }
    }

    @keyframes ring {
        from, to {
            transform: rotate(0deg);
        }
    }
}

/* Print styles */
@media print {
    .page-header,
    .stats-section,
    .benefits-section {
        background: white !important;
        color: black !important;
    }

    .course-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    .start-course-btn,
    .btn-primary,
    .btn-secondary {
        background: white !important;
        color: black !important;
        border: 1px solid #000;
    }
}
