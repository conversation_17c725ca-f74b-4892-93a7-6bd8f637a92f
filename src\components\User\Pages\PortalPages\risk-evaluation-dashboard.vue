<!-- src/components/User/Pages/PortalPages/risk-evaluation-dashboard.vue -->
<template>
    <div class="risk-dashboard">
        <!-- Page Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">
                        <i class="fa fa-shield title-icon"></i>
                        Risk Evaluation
                    </h1>
                    <p class="page-subtitle">
                        Assess and analyze AI systems for potential risks and vulnerabilities
                    </p>
                </div>
                <div class="header-actions">
                    <router-link :to="{ name: 'StartRiskEvaluation' }" class="btn-primary">
                        <i class="fa fa-plus"></i>
                        New Evaluation
                    </router-link>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
            <div class="loading-spinner">
                <i class="fa fa-spinner fa-spin"></i>
            </div>
            <p class="loading-text">Loading your evaluations...</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="evaluations.length === 0" class="empty-state">
            <div class="empty-content">
                <div class="empty-illustration">
                    <div class="shield-icon">
                        <i class="fa fa-shield"></i>
                    </div>
                    <div class="floating-elements">
                        <div class="element element-1"></div>
                        <div class="element element-2"></div>
                        <div class="element element-3"></div>
                    </div>
                </div>
                <h2 class="empty-title">No Risk Evaluations Yet</h2>
                <p class="empty-description">
                    Start your first AI risk evaluation to identify potential vulnerabilities 
                    and security concerns in your AI systems.
                </p>
                <div class="empty-actions">
                    <router-link :to="{ name: 'StartRiskEvaluation' }" class="btn-primary large">
                        <i class="fa fa-shield"></i>
                        Start Your First Risk Evaluation
                    </router-link>
                    <a href="https://raidot.ai" target="_blank" class="btn-secondary">
                        <i class="fa fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>
        </div>

        <!-- Evaluations Grid -->
        <div v-else class="evaluations-section">
            <div class="section-header">
                <h2 class="section-title">Your Risk Evaluations</h2>
                <div class="evaluation-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ evaluations.length }}</span>
                        <span class="stat-label">Total Evaluations</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{{ completedEvaluations }}</span>
                        <span class="stat-label">Completed</span>
                    </div>
                </div>
            </div>

            <div class="evaluations-grid">
                <!-- New Evaluation Card -->
                <div class="evaluation-card new-evaluation">
                    <router-link :to="{ name: 'StartRiskEvaluation' }" class="card-link">
                        <div class="card-content">
                            <div class="new-evaluation-icon">
                                <i class="fa fa-plus"></i>
                            </div>
                            <h3 class="card-title">New Risk Evaluation</h3>
                            <p class="card-description">
                                Start a comprehensive risk assessment for your AI system
                            </p>
                        </div>
                        <div class="card-footer">
                            <span class="action-text">Create Evaluation</span>
                            <i class="fa fa-arrow-right"></i>
                        </div>
                    </router-link>
                </div>

                <!-- Evaluation Cards -->
                <div 
                    v-for="evaluation in evaluations" 
                    :key="evaluation._id"
                    class="evaluation-card"
                    :class="getEvaluationCardClass(evaluation)"
                >
                    <div class="card-header">
                        <div class="evaluation-type">
                            <div class="type-icon" :class="getTypeIconClass(evaluation.category)">
                                <i class="fa fa-shield"></i>
                            </div>
                            <span class="type-label">{{ getEvaluationType(evaluation.category) }}</span>
                        </div>
                        <div class="card-actions">
                            <button 
                                @click="deleteEvaluation(evaluation)" 
                                class="delete-btn"
                                :title="`Delete ${evaluation.project.name}`"
                            >
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="card-content">
                        <h3 class="card-title">{{ evaluation.project.name }}</h3>
                        <p class="card-description" v-if="evaluation.project.desc">
                            {{ evaluation.project.desc }}
                        </p>
                        <div class="evaluation-meta">
                            <div class="meta-item">
                                <i class="fa fa-calendar"></i>
                                <span>{{ formatDate(evaluation.created_at) || 'No date available' }}</span>
                            </div>
                            <div class="meta-item" v-if="evaluation.project.website">
                                <i class="fa fa-globe"></i>
                                <span>{{ getDomainFromUrl(evaluation.project.website) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <router-link 
                            :to="{ name: 'EvaluationReport', params: { evaluation_id: evaluation._id } }"
                            class="view-report-btn"
                        >
                            <span>View Report</span>
                            <i class="fa fa-external-link"></i>
                        </router-link>
                    </div>

                    <!-- Risk Level Indicator -->
                    <div class="risk-indicator" v-if="evaluation.risk_level">
                        <div 
                            class="risk-badge" 
                            :class="`risk-${evaluation.risk_level.toLowerCase()}`"
                        >
                            {{ evaluation.risk_level }} Risk
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ApiService from "@/services/ApiService";
import ApiRoutes from "@/ApiRoutes";
import Swal from "sweetalert2";

export default {
    name: 'RiskEvaluationDashboard',
    data() {
        return {
            loading: true,
            evaluations: []
        }
    },
    computed: {
        completedEvaluations() {
            return this.evaluations.filter(evaluation => {
                // Check if evaluation has a report with riskPercentage and No risk = 100%
                return evaluation.report && 
                    evaluation.report.riskPercentage && 
                    evaluation.report.riskPercentage.No === 100;
            }).length;
        }
    },
    methods: {
        async loadEvaluations() {
            this.loading = true;
            try {
                ApiService.GET(ApiRoutes.UserEvaluations, (res) => {
                    if (res.status === 200) {
                        // Filter for risk evaluations only
                        this.evaluations = res.data.filter(evaluation => 
                            ['et', 'eta', 'nt'].includes(evaluation.category)
                        );
                    }
                    this.loading = false;
                });
            } catch (error) {
                console.error('Error loading evaluations:', error);
                this.loading = false;
            }
        },

        deleteEvaluation(evaluation) {
            Swal.fire({
                title: 'Delete Evaluation',
                html: `
                    <p>Are you sure you want to delete "<strong>${evaluation.project.name}</strong>"?</p>
                    <p class="text-danger small">This action cannot be undone.</p>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, Delete',
                cancelButtonText: 'Cancel',
                customClass: {
                    popup: 'rounded-lg',
                    confirmButton: 'rounded-lg',
                    cancelButton: 'rounded-lg'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    this.loading = true;
                    ApiService.POST(ApiRoutes.UserEvaluationDelete, 
                        { evaluation_id: evaluation._id }, 
                        (res) => {
                            if (res.status === 200) {
                                this.loadEvaluations();
                                Swal.fire({
                                    title: 'Deleted!',
                                    text: 'Evaluation has been deleted successfully.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            }
                        }
                    );
                }
            });
        },

        getEvaluationType(category) {
            const types = {
                'et': 'General Technical',
                'eta': 'Industry Specific',
                'nt': 'Non-Technical'
            };
            return types[category] || 'Risk Evaluation';
        },

        getTypeIconClass(category) {
            const classes = {
                'et': 'type-general',
                'eta': 'type-industry',
                'nt': 'type-basic'
            };
            return classes[category] || 'type-general';
        },

        getEvaluationCardClass(evaluation) {
            return `evaluation-${evaluation.category}`;
        },

        formatDate(dateString) {
            if (!dateString) return 'No date available';
            
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid date';
            
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },
        
        getDomainFromUrl(url) {
            try {
                const domain = new URL(url).hostname;
                return domain.replace('www.', '');
            } catch {
                return url;
            }
        }
    },
    created() {
        localStorage.setItem('risk_analysis', 'risk');
        this.loadEvaluations();
    },
    mounted() {
        window.scrollTo(0, 0);
    }
}
</script>

<style scoped>
.risk-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

.btn-primary.large {
    padding: 1.125rem 2.5rem;
    font-size: 1.125rem;
}

.btn-secondary {
    background: white;
    color: #ef4444;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-secondary:hover {
    background: #fef2f2;
    border-color: #ef4444;
    transform: translateY(-2px);
    color: #dc2626;
    text-decoration: none;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
}

.loading-spinner {
    font-size: 3rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.loading-text {
    color: #6b7280;
    font-size: 1.125rem;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
}

.empty-illustration {
    position: relative;
    margin-bottom: 2rem;
    display: inline-block;
}

.shield-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #ef4444;
    position: relative;
    z-index: 1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    opacity: 0.1;
    animation: float 3s ease-in-out infinite;
}

.element-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation-delay: -1s;
}

.element-2 {
    width: 16px;
    height: 16px;
    top: 60%;
    right: 20%;
    animation-delay: -2s;
}

.element-3 {
    width: 12px;
    height: 12px;
    bottom: 20%;
    left: 30%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.empty-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto 2.5rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Evaluations Section */
.evaluations-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.evaluation-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #ef4444;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.evaluations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Evaluation Cards */
.evaluation-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.evaluation-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #ef4444;
}

.evaluation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.evaluation-card:hover::before {
    left: 100%;
}

/* New Evaluation Card */
.new-evaluation {
    border: 2px dashed #d1d5db;
    background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
}

.new-evaluation:hover {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.card-link {
    display: block;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    height: 100%;
    position: relative;
    z-index: 2;
}

.new-evaluation-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

/* Regular Evaluation Cards */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.evaluation-type {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.type-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.type-general {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.type-industry {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
}

.type-basic {
    background: linear-gradient(135deg, #059669, #047857);
}

.type-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.delete-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-btn:hover {
    background: #ef4444;
    color: white;
    transform: scale(1.1);
}

.card-content {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.card-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.evaluation-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.meta-item i {
    width: 16px;
    color: #9ca3af;
}

.card-footer {
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.view-report-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.875rem 1.25rem;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.view-report-btn:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    color: white;
    text-decoration: none;
}

.view-report-btn i {
    transition: transform 0.3s ease;
}

.view-report-btn:hover i {
    transform: translateX(4px);
}

/* Risk Indicator */
.risk-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 3;
}

.risk-badge {
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
}

.risk-low {
    background: linear-gradient(135deg, #10b981, #059669);
}

.risk-medium {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.risk-high {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.risk-critical {
    background: linear-gradient(135deg, #7c2d12, #991b1b);
}

/* Evaluation Type Variants */
.evaluation-et {
    border-left: 4px solid #3b82f6;
}

.evaluation-eta {
    border-left: 4px solid #7c3aed;
}

.evaluation-nt {
    border-left: 4px solid #059669;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .evaluation-stats {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .evaluations-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .evaluation-stats {
        justify-content: center;
        width: 100%;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .card-content {
        padding: 1.25rem;
    }
    
    .card-footer {
        padding: 0 1.25rem 1.25rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-link,
    .card-content {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem 1rem 1rem;
    }
    
    .card-footer {
        padding: 1rem 1rem 1rem;
    }
    
    .evaluation-meta {
        gap: 0.375rem;
    }
    
    .meta-item {
        font-size: 0.8125rem;
    }
}

/* Enhanced animations and micro-interactions */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.evaluation-card {
    animation: slideInUp 0.6s ease-out;
}

.evaluation-card:nth-child(1) { animation-delay: 0.1s; }
.evaluation-card:nth-child(2) { animation-delay: 0.2s; }
.evaluation-card:nth-child(3) { animation-delay: 0.3s; }
.evaluation-card:nth-child(4) { animation-delay: 0.4s; }
.evaluation-card:nth-child(5) { animation-delay: 0.5s; }
.evaluation-card:nth-child(6) { animation-delay: 0.6s; }

/* Focus states for accessibility */
.card-link:focus,
.view-report-btn:focus,
.delete-btn:focus {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
}

/* Loading animation for cards */
.evaluation-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.evaluation-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #ef4444;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
</style>