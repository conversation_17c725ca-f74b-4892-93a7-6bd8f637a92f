import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import AwarenessService from '@/services/awareness/AwarenessService';
import { createToaster } from "@meforma/vue-toaster";
import Swal from 'sweetalert2';

export const useAwarenessActions = () => {
  const Toaster = createToaster({ position: 'top-right' });
  const router = useRouter();

  const loading = ref(false);
  const courses = ref([]);
  const currentCourse = ref(null);
  const topics = ref([]);
  const currentLesson = ref(null);
  const searchQuery = ref('');
  const courseProgressCache = new Map();

  const withLoading = async (operation) => {
    loading.value = true;
    try {
      return await operation();
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  const filteredCourses = computed(() => {
    if (!searchQuery.value) return courses.value;
    
    return courses.value.filter(course => 
      course.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      course.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  });

  const totalCertificates = computed(() => {
    return courses.value?.filter(course => course.certificate_eligible)?.length || 0;
  });

  const completedCoursesCount = computed(() => {
    return courses.value?.filter(course => (course.completion_percentage || 0) === 100)?.length || 0;
  });

  const getAwarenessEvaluations = async () => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getAwarenessEvaluations();
      courses.value = data;
      return { data };
    });
  };

  const getAwarenessEvaluationsWithProgress = async () => {
    return withLoading(async () => {
      const { data: rawCourses } = await AwarenessService.getAwarenessEvaluations();
      
      const enhancedCourses = [];
      for (const course of rawCourses) {
        try {
          const enhancedCourse = await enhanceCourseWithProgress(course);
          enhancedCourses.push(enhancedCourse);
        } catch (error) {
          console.error('Error enhancing course data for:', course._id, error);
          enhancedCourses.push({
            ...course,
            completion_percentage: 0,
            total_lessons: 0,
            completed_lessons: 0,
            certificate_eligible: false,
            last_activity: course.created_at || new Date().toISOString()
          });
        }
      }
      
      courses.value = enhancedCourses;
      return { data: enhancedCourses };
    });
  };

  const enhanceCourseWithProgress = async (course) => {
    if (courseProgressCache.has(course._id)) {
      return {
        ...course,
        ...courseProgressCache.get(course._id)
      };
    }

    const enhancedData = {
      completion_percentage: 0,
      total_lessons: 0,
      completed_lessons: 0,
      certificate_eligible: false,
      last_activity: course.created_at
    };

    try {
      const { data: topicsData } = await AwarenessService.getCourseTopics(course._id);
      if (topicsData && topicsData.length > 0) {
        let totalLessons = 0;
        let completedLessons = 0;
        let hasCompletedFinalExam = false;

        topicsData.forEach(topic => {
          if (topic.lessons && Array.isArray(topic.lessons)) {
            topic.lessons.forEach(lesson => {
              totalLessons++;
              if (lesson.completed === 1) {
                completedLessons++;
                if (lesson.is_final_exam === '1' || lesson.lesson_type === 'final_exam') {
                  hasCompletedFinalExam = true;
                }
              }
            });
          }
        });

        enhancedData.total_lessons = totalLessons;
        enhancedData.completed_lessons = completedLessons;
        enhancedData.completion_percentage = totalLessons > 0 ?
          Math.round((completedLessons / totalLessons) * 100) : 0;
        enhancedData.certificate_eligible = (completedLessons === totalLessons && hasCompletedFinalExam);
        
        courseProgressCache.set(course._id, enhancedData);
      }
    } catch (error) {
      console.error('Error fetching course topics for progress:', error);
    }

    return {
      ...course,
      ...enhancedData
    };
  };

  const getAvailableCourses = async () => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getAvailableCourses();
      courses.value = data;
      return { data };
    });
  };

  const getSingleCourse = async (courseId) => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getSingleCourse(courseId);
      currentCourse.value = data;
      return { data };
    });
  };

  const getCourseTopics = async (courseId) => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getCourseTopics(courseId);
      topics.value = data;
      return { data };
    });
  };

  const getSingleLesson = async (courseId, topicId, lessonId) => {
    return withLoading(async () => {
      const { data } = await AwarenessService.getSingleLesson(courseId, topicId, lessonId);
      currentLesson.value = data;
      return { data };
    });
  };

  const completeLesson = async (courseId, topicId, lessonId) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessService.completeLesson(courseId, topicId, lessonId);
      return { data, msg };
    });
  };

  const submitQuiz = async (courseId, topicId, lessonId, questions) => {
    return withLoading(async () => {
      const { data, msg } = await AwarenessService.submitQuiz(courseId, topicId, lessonId, questions);
      return { data, msg };
    });
  };

  const downloadCourseCertificate = async (courseId) => {
    return withLoading(async () => {
      const response = await AwarenessService.downloadCourseCertificate(courseId);
      
      const disposition = response.headers['content-disposition'];
      let filename = 'certificate.pdf';
      if (disposition && disposition.includes('filename=')) {
        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch != null && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      const file = new Blob([response.data], { type: 'application/pdf' });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(file);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return response;
    });
  };

  const canAccessCourse = (course) => {
    const currentPackage = JSON.parse(localStorage.getItem("Subscription"));
    if (!currentPackage || !course.package) return true;
    
    return course.package.package_level <= currentPackage.package_level;
  };

  const navigateToCourse = (course) => {
    if (!canAccessCourse(course)) {
      console.error('This course requires a higher subscription level. Please upgrade your plan.');
      router.push('/subscribe');
      return;
    }
    
    router.push(`/portal/awareness-evaluation/course/${course._id}`);
  };

  const startCourse = (course) => {
    if (!canAccessCourse(course)) {
      console.error('This course requires a higher subscription level. Please upgrade your plan.');
      router.push('/subscribe');
      return;
    }

    router.push(`/portal/awareness-evaluation/course/${course._id}`);
  };

  const getOverallProgress = (topicsData) => {
    if (!topicsData || !topicsData.length) return 0;
    const totalLessons = getTotalLessons(topicsData);
    const completedLessons = getCompletedLessons(topicsData);
    return totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
  };

  const getTotalLessons = (topicsData) => {
    return topicsData.reduce((total, topic) => {
      return total + (topic.lessons ? topic.lessons.length : 0);
    }, 0);
  };

  const getCompletedLessons = (topicsData) => {
    return topicsData.reduce((total, topic) => {
      if (!topic.lessons) return total;
      return total + topic.lessons.filter(lesson => lesson.completed === 1).length;
    }, 0);
  };

  const getTopicProgress = (topic) => {
    if (!topic.lessons) return '0/0';
    const totalLessons = topic.lessons.length;
    const completedLessons = topic.lessons.filter(lesson => lesson.completed === 1).length;
    return `${completedLessons}/${totalLessons}`;
  };

  const getFirstAvailableLesson = (topicsData) => {
    for (const topic of topicsData) {
      if (!topic.lessons) continue;
      for (const lesson of topic.lessons) {
        if (lesson.readable === 1) {
          return { topic, lesson };
        }
      }
    }
    return null;
  };

  const startFirstLesson = (course, topicsData) => {
    const firstLesson = getFirstAvailableLesson(topicsData);
    if (firstLesson) {
      router.push({
        name: 'LessonPreview',
        params: {
          course_id: course._id,
          topic_id: firstLesson.topic._id,
          lesson_id: firstLesson.lesson._id
        }
      });
    }
  };

  const continueFromLastLesson = (course, topicsData) => {
    for (const topic of topicsData) {
      if (!topic.lessons) continue;
      for (const lesson of topic.lessons) {
        if (lesson.readable === 1 && lesson.completed === 0) {
          router.push({
            name: 'LessonPreview',
            params: {
              course_id: course._id,
              topic_id: topic._id,
              lesson_id: lesson._id
            }
          });
          return;
        }
      }
    }
  };

  const getStatusIcon = (completionPercentage) => {
    if (completionPercentage === 100) {
      return 'fa-check-circle';
    } else if (completionPercentage > 0) {
      return 'fa-play-circle';
    } else {
      return 'fa-circle-o';
    }
  };

  const getStatusLabel = (completionPercentage) => {
    if (completionPercentage === 100) {
      return 'Completed';
    } else if (completionPercentage > 0) {
      return 'In Progress';
    } else {
      return 'Not Started';
    }
  };

  const getProgressClass = (percentage) => {
    if (percentage === 100) return 'progress-complete';
    if (percentage > 50) return 'progress-good';
    if (percentage > 0) return 'progress-started';
    return 'progress-none';
  };

  const getStatusClass = (completionPercentage) => {
    if (completionPercentage === 100) {
      return 'completed';
    } else if (completionPercentage > 0) {
      return 'in-progress';
    } else {
      return 'not-started';
    }
  };

  const getProgressColor = (percentage) => {
    if (percentage === 100) return '#10b981';
    if (percentage > 50) return '#f59e0b';
    if (percentage > 0) return '#3b82f6';
    return '#e5e7eb';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const getCertificateStatus = (course) => {
    if (course.certificate_eligible) {
      return 'Available';
    } else if (course.completion_percentage === 100) {
      return 'Processing';
    } else {
      return 'Not Available';
    }
  };

  const confirmMarkAsComplete = () => {
    return Swal.fire({
      title: 'Mark as Complete',
      text: "Are you sure you want to mark this lesson as complete?",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Complete',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-lg',
        confirmButton: 'rounded-lg',
        cancelButton: 'rounded-lg'
      }
    });
  };

  const showLessonCompleted = (message) => {
    return Swal.fire({
      title: 'Completed!',
      text: message || 'Lesson marked as complete successfully.',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const showError = (message) => {
    return Swal.fire({
      title: 'Error',
      text: message || 'An error occurred.',
      icon: 'error',
      confirmButtonColor: '#059669'
    });
  };

  const confirmSubmitQuiz = () => {
    return Swal.fire({
      title: 'Submit Quiz',
      text: "Are you sure you want to submit your answers? You cannot change them after submission.",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Submit Quiz',
      cancelButtonText: 'Review Answers'
    });
  };

  const showIncompleteQuiz = () => {
    return Swal.fire({
      title: 'Incomplete Quiz',
      text: 'Please answer all questions before submitting.',
      icon: 'warning',
      confirmButtonColor: '#059669'
    });
  };

  const showQuizSubmitted = (score) => {
    return Swal.fire({
      title: 'Quiz Submitted!',
      text: `You scored ${score}%`,
      icon: score >= 80 ? 'success' : 'info',
      confirmButtonColor: '#059669'
    });
  };

  const confirmRetakeQuiz = () => {
    return Swal.fire({
      title: 'Retake Quiz',
      text: "Are you sure you want to retake this quiz? Your previous score will be replaced.",
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#059669',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Retake Quiz',
      cancelButtonText: 'Cancel'
    });
  };

  const showCertificateDownloaded = () => {
    return Swal.fire({
      title: 'Certificate Downloaded!',
      text: 'Your certificate has been downloaded successfully.',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    });
  };

  const allQuestionsAnswered = (questions) => {
    return questions.every(q => q.answer);
  };

  const getScoreClass = (score) => {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 70) return 'average';
    return 'needs-improvement';
  };

  const getScoreMessage = (score) => {
    if (score >= 90) return 'Excellent Work!';
    if (score >= 80) return 'Great Job!';
    if (score >= 70) return 'Good Effort!';
    return 'Keep Learning!';
  };

  const getScoreDescription = (score) => {
    if (score >= 90) return 'Outstanding performance! You have mastered this topic.';
    if (score >= 80) return 'Well done! You have a solid understanding of the material.';
    if (score >= 70) return 'Good work! Consider reviewing the material to strengthen your knowledge.';
    return 'Don\'t give up! Review the material and try again.';
  };

  return {
    loading,
    courses,
    currentCourse,
    topics,
    currentLesson,
    searchQuery,
    filteredCourses,
    totalCertificates,
    completedCoursesCount,
    getAwarenessEvaluations,
    getAwarenessEvaluationsWithProgress,
    enhanceCourseWithProgress,
    getAvailableCourses,
    getSingleCourse,
    getCourseTopics,
    getSingleLesson,
    completeLesson,
    submitQuiz,
    downloadCourseCertificate,
    canAccessCourse,
    navigateToCourse,
    startCourse,
    getOverallProgress,
    getTotalLessons,
    getCompletedLessons,
    getTopicProgress,
    getFirstAvailableLesson,
    startFirstLesson,
    continueFromLastLesson,
    getStatusIcon,
    getStatusLabel,
    getProgressClass,
    getStatusClass,
    getProgressColor,
    formatDate,
    getCertificateStatus,
    confirmMarkAsComplete,
    showLessonCompleted,
    showError,
    confirmSubmitQuiz,
    showIncompleteQuiz,
    showQuizSubmitted,
    confirmRetakeQuiz,
    showCertificateDownloaded,
    allQuestionsAnswered,
    getScoreClass,
    getScoreMessage,
    getScoreDescription
  };
};
