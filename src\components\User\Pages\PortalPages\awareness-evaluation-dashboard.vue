<template>
    <div class="awareness-dashboard">
        <PageHeader
            title="Awareness Training"
            subtitle="Enhance your AI knowledge through comprehensive training courses and assessments"
            icon="fa-graduation-cap"
        >
            <template #actions>
                <router-link :to="{ name: 'CourseList' }" class="btn-primary">
                    <i class="fa fa-plus"></i> Start Course
                </router-link>
            </template>
        </PageHeader>

        <LoadingSpinner v-if="loading" message="Loading your courses..." />

        <div v-else-if="courses.length === 0" class="empty-state">
            <div class="empty-content">
                <div class="empty-illustration">
                    <div class="cap-icon">
                        <i class="fa fa-graduation-cap"></i>
                    </div>
                    <div class="floating-elements">
                        <div class="element element-1"></div>
                        <div class="element element-2"></div>
                        <div class="element element-3"></div>
                    </div>
                </div>
                <h2 class="empty-title">No Started Courses Yet</h2>
                <p class="empty-description">
                    Start your AI awareness journey by taking comprehensive training courses
                    and earn certificates to validate your knowledge.
                </p>
                <div class="empty-actions">
                    <router-link :to="{ name: 'CourseList' }" class="btn-primary large">
                        <i class="fa fa-graduation-cap"></i>
                        Explore Available Courses
                    </router-link>
                    <a href="https://raidot.ai" target="_blank" class="btn-secondary">
                        <i class="fa fa-info-circle"></i>
                        Learn More
                    </a>
                </div>
            </div>
        </div>

        <div v-else class="courses-section">
            <div class="section-header">
                <h2 class="section-title">Your Learning Progress</h2>
                <CourseStats
                    :total-courses="courses.length"
                    :completed-courses="completedCoursesCount"
                    :total-certificates="totalCertificates"
                />
            </div>

            <div class="courses-grid">
                <div class="course-card new-course">
                    <router-link :to="{ name: 'CourseList' }" class="card-link">
                        <div class="card-content">
                            <div class="new-course-icon">
                                <i class="fa fa-plus"></i>
                            </div>
                            <h3 class="card-title">Start New Course</h3>
                            <p class="card-description">
                                Explore available training courses and expand your AI knowledge
                            </p>
                        </div>
                        <div class="card-footer">
                            <span class="action-text">Browse Courses</span>
                            <i class="fa fa-arrow-right"></i>
                        </div>
                    </router-link>
                </div>

                <CourseCard
                    v-for="course in courses"
                    :key="course._id"
                    :course="course"
                    route-name="AwarenessCoursePreview"
                    @course-click="handleCourseClick"
                    @certificate-download="handleCertificateDownload"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAwarenessActions } from '@/composables/awareness/userAwareness/useAwarenessActions';
import PageHeader from '@/components/General/PageHeader.vue';
import LoadingSpinner from '@/components/General/LoadingSpinner.vue';
import CourseCard from '@/components/General/CourseCard.vue';
import CourseStats from '@/components/General/CourseStats.vue';

const router = useRouter();

const {
    loading,
    courses,
    totalCertificates,
    completedCoursesCount,
    getAwarenessEvaluationsWithProgress,
    navigateToCourse,
    downloadCourseCertificate,
    showCertificateDownloaded,
    showError
} = useAwarenessActions();

const handleCourseClick = (course) => {
    navigateToCourse(course);
};

const handleCertificateDownload = async (course) => {
    try {
        await downloadCourseCertificate(course._id);
        await showCertificateDownloaded();
    } catch (error) {
        await showError('Failed to download certificate.');
    }
};

const loadCourses = async () => {
    try {
        await getAwarenessEvaluationsWithProgress();
    } catch (error) {
        console.error('Error loading courses:', error);
        await showError('Failed to load courses.');
    }
};

onMounted(async () => {
    await loadCourses();
});
</script>

<style scoped>
@import '@/assets/scss/front/components/PortalPages/awareness-evaluation-dashboard.scss';
</style>