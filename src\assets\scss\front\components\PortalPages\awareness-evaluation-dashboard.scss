.awareness-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* <PERSON> Header */
.page-header {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: white;
    text-decoration: none;
}

.btn-primary.large {
    padding: 1.125rem 2.5rem;
    font-size: 1.125rem;
}

.btn-secondary {
    background: white;
    color: #059669;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-secondary:hover {
    background: #f0fdf4;
    border-color: #059669;
    transform: translateY(-2px);
    color: #047857;
    text-decoration: none;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
}

.loading-spinner {
    font-size: 3rem;
    color: #059669;
    margin-bottom: 1rem;
}

.loading-text {
    color: #6b7280;
    font-size: 1.125rem;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 24px;
    border: 1px solid #e5e7eb;
}

.empty-illustration {
    position: relative;
    margin-bottom: 2rem;
    display: inline-block;
}

.cap-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #ecfdf5, #d1fae5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #059669;
    position: relative;
    z-index: 1;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #059669, #047857);
    opacity: 0.1;
    animation: float 3s ease-in-out infinite;
}

.element-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation-delay: -1s;
}

.element-2 {
    width: 16px;
    height: 16px;
    top: 60%;
    right: 20%;
    animation-delay: -2s;
}

.element-3 {
    width: 12px;
    height: 12px;
    bottom: 20%;
    left: 30%;
    animation-delay: -0.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.empty-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.empty-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
    max-width: 500px;
    margin: 0 auto 2.5rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Courses Section */
.courses-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.course-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Course Cards */
.course-card {
    background: white;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

.course-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #059669;
}

.course-card.completed-course {
    border-color: #059669;
    box-shadow: 0 4px 20px rgba(5, 150, 105, 0.1);
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.course-card:hover::before {
    left: 100%;
}

/* New Course Card */
.new-course {
    border: 2px dashed #d1d5db;
    background: linear-gradient(135deg, #fafafa 0%, #f3f4f6 100%);
}

.new-course:hover {
    border-color: #059669;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.card-link {
    display: block;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    height: 100%;
    position: relative;
    z-index: 2;
}

.new-course-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #059669, #047857);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

/* Regular Course Cards */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.course-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.status-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.status-icon.completed {
    background: linear-gradient(135deg, #059669, #047857);
}

.status-icon.in-progress {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-icon.started {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.status-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
}

.progress-badge {
    background: rgba(5, 150, 105, 0.1);
    color: #059669;
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 700;
}

.card-content {
    padding: 1.5rem;
    position: relative;
    z-index: 2;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.75rem;
    line-height: 1.3;
}

.card-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.progress-container {
    margin-top: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-fill.completed {
    background: linear-gradient(135deg, #059669, #047857);
}

.progress-fill.high {
    background: linear-gradient(135deg, #10b981, #059669);
}

.progress-fill.medium {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.progress-fill.low {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.progress-text {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

.course-meta {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.meta-item i {
    width: 16px;
    color: #9ca3af;
}

.card-footer {
    padding: 1.5rem 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

.continue-btn,
.download-cert-btn,
.review-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.875rem 1.25rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.continue-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.continue-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
}

.download-cert-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
}

.download-cert-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
}

.review-btn {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.review-btn:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    color: white;
    text-decoration: none;
}

.action-text {
    flex: 1;
    text-align: left;
}

/* Completion Ring */
.completion-ring {
    position: absolute;
    bottom: 7rem;
    right: 1rem;
    z-index: 3;
}

.progress-circle {
    position: relative;
    width: 50px;
    height: 50px;
}

.circular-chart {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e5e7eb;
    stroke-width: 3;
}

.circle {
    fill: none;
    stroke: #059669;
    stroke-width: 3;
    stroke-linecap: round;
    animation: progress 1s ease-in-out;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1rem;
    color: #059669;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .course-stats {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }
    
    .page-title {
        font-size: 1.875rem;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .course-stats {
        justify-content: center;
        width: 100%;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .card-content {
        padding: 1.25rem;
    }
    
    .card-footer {
        padding: 0 1.25rem 1.25rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1.5rem 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-link,
    .card-content {
        padding: 1rem;
    }
    
    .card-header {
        padding: 1rem 1rem 0;
    }
    
    .card-footer {
        padding: 0 1rem 1rem;
    }
    
    .course-meta {
        gap: 0.375rem;
    }
    
    .meta-item {
        font-size: 0.8125rem;
    }
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }
.course-card:nth-child(5) { animation-delay: 0.5s; }
.course-card:nth-child(6) { animation-delay: 0.6s; }

/* Focus states for accessibility */
.card-link:focus,
.continue-btn:focus,
.download-cert-btn:focus,
.review-btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .course-card {
        border-width: 2px;
    }
    
    .status-icon {
        filter: contrast(1.2);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .course-card {
        animation: none;
    }
    
    .progress-fill,
    .continue-btn,
    .download-cert-btn,
    .review-btn {
        transition: none;
    }
    
    @keyframes progress {
        from, to {
            stroke-dasharray: 0 100;
        }
    }
    
    @keyframes float {
        from, to {
            transform: translateY(0);
        }
    }
}

/* Print styles */
@media print {
    .page-header {
        background: white !important;
        color: black !important;
    }
    
    .course-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .continue-btn,
    .download-cert-btn,
    .review-btn {
        background: white !important;
        color: black !important;
        border: 1px solid black;
    }
}