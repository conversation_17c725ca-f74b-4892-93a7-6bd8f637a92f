<template>
  <div class="consultancy-bookings-management">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="h3 mb-1">Consultancy Bookings</h2>
        <p class="text-muted mb-0">Manage all consultancy session bookings</p>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Total Bookings</h6>
                <h4 class="mb-0">{{ stats.total_bookings }}</h4>
              </div>
              <div class="bg-primary bg-opacity-10 p-3 rounded">
                <i class="fa fa-calendar text-primary"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Confirmed</h6>
                <h4 class="mb-0">{{ stats.confirmed_bookings }}</h4>
              </div>
              <div class="bg-success bg-opacity-10 p-3 rounded">
                <i class="fa fa-check text-success"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Pending</h6>
                <h4 class="mb-0">{{ stats.pending_bookings }}</h4>
              </div>
              <div class="bg-warning bg-opacity-10 p-3 rounded">
                <i class="fa fa-clock text-warning"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h6 class="text-muted mb-1">Today's Sessions</h6>
                <h4 class="mb-0">{{ stats.today_bookings }}</h4>
              </div>
              <div class="bg-info bg-opacity-10 p-3 rounded">
                <i class="fa fa-users text-info"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-body">
        <div class="row g-3">
          <div class="col-md-3">
            <select v-model="filters.status" class="form-select">
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          <div class="col-md-3">
            <input
              type="date"
              v-model="filters.date_from"
              class="form-control"
              placeholder="From Date"
            />
          </div>
          <div class="col-md-3">
            <input
              type="date"
              v-model="filters.date_to"
              class="form-control"
              placeholder="To Date"
            />
          </div>
          <div class="col-md-3">
            <select v-model="filters.type" class="form-select">
              <option value="">All Types</option>
              <option value="online">Online</option>
              <option value="in_person">In-Person</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Bookings List -->
    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <div v-else-if="bookings.length > 0" class="row">
      <div v-for="booking in bookings" :key="booking._id" class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
          <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-start">
            <div class="d-flex align-items-center">
              <div class="d-flex align-items-center justify-content-center bg-primary bg-opacity-10 rounded-circle me-3" style="width: 50px; height: 50px;">
                <i class="fa fa-user text-primary"></i>
              </div>
              <div>
                <h6 class="mb-1">{{ booking.user.name }}</h6>
                <p class="text-muted mb-1 small">{{ booking.user.email }}</p>
                <span :class="getPackageColor(booking.user.package_name)" class="badge">
                  {{ booking.user.package_name }}
                </span>
              </div>
            </div>
            <div class="text-end">
              <span :class="getStatusColor(booking.status)" class="badge mb-1">
                {{ booking.status.charAt(0).toUpperCase() + booking.status.slice(1) }}
              </span>
              <p class="text-muted small mb-0">{{ booking.booking_reference }}</p>
            </div>
          </div>

          <div class="card-body pt-0">
            <div class="mb-3">
              <div class="d-flex align-items-center mb-2">
                <i class="fa fa-calendar me-2 text-muted"></i>
                <span class="small">{{ formatDate(booking.slot.date) }}</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <i class="fa fa-clock me-2 text-muted"></i>
                <span class="small">{{ booking.slot.start_time }} - {{ booking.slot.end_time }}</span>
                <span class="badge bg-light text-dark ms-2 small">{{ booking.duration_minutes }}min</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <i :class="booking.slot.type === 'online' ? 'fa fa-video' : 'fa fa-map-marker'" class="me-2 text-muted"></i>
                <span class="small">
                  {{ booking.slot.type === 'online' 
                      ? 'Online Session' 
                      : `${booking.slot.location}, ${booking.slot.city}` }}
                </span>
              </div>
              
              <div v-if="booking.notes" class="mt-3">
                <div class="bg-light p-3 rounded">
                  <small class="fw-bold text-muted">Notes:</small>
                  <p class="small mb-0 mt-1">{{ booking.notes }}</p>
                </div>
              </div>
            </div>

            <!-- Meeting Link Display -->
            <div v-if="booking.meeting_link" class="mb-3">
              <div class="bg-primary bg-opacity-10 p-3 rounded">
                <div class="d-flex justify-content-between align-items-center">
                  <small class="fw-bold text-primary">Meeting Link:</small>
                  <a
                    :href="booking.meeting_link"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="btn btn-sm btn-primary"
                  >
                    <i class="fa fa-external-link me-1"></i>
                    Open
                  </a>
                </div>
                <p class="small text-primary mb-0 mt-1 text-break">{{ booking.meeting_link }}</p>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex flex-wrap gap-2">
              <!-- Confirm Button -->
              <button
                v-if="booking.status === 'pending'"
                @click="confirmBooking(booking._id)"
                class="btn btn-sm btn-success"
              >
                <i class="fa fa-check me-1"></i>
                Confirm
              </button>
              
              <!-- Cancel Button -->
              <button
                v-if="booking.status !== 'cancelled'"
                @click="cancelBooking(booking._id)"
                class="btn btn-sm btn-danger"
              >
                <i class="fa fa-times me-1"></i>
                Cancel
              </button>

              <!-- Meeting Link Actions (for online sessions) -->
              <template v-if="booking.slot.type === 'online'">
                <button
                  @click="openMeetingLinkModal(booking)"
                  class="btn btn-sm btn-primary"
                >
                  <i class="fa fa-edit me-1"></i>
                  Meeting Link
                </button>
                
                <button
                  v-if="booking.meeting_link"
                  @click="sendMeetingLink(booking._id)"
                  class="btn btn-sm btn-info"
                >
                  <i class="fa fa-envelope me-1"></i>
                  Send Link
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-5">
      <i class="fa fa-calendar fa-3x text-muted mb-3"></i>
      <h5 class="text-muted">No bookings found</h5>
      <p class="text-muted">No consultancy bookings match your current filters.</p>
    </div>

    <!-- Meeting Link Modal -->
    <div
      class="modal fade"
      :class="{ show: showMeetingLinkModal }"
      :style="{ display: showMeetingLinkModal ? 'block' : 'none' }"
      tabindex="-1"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Update Meeting Link</h5>
            <button
              type="button"
              class="btn-close"
              @click="closeMeetingLinkModal"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedBooking">
              <div class="mb-3">
                <label class="form-label">
                  Meeting Link for {{ selectedBooking.user.name }}
                </label>
                <input
                  type="url"
                  v-model="meetingLink"
                  class="form-control"
                  placeholder="https://meet.example.com/meeting-id"
                />
              </div>

              <div class="bg-light p-3 rounded">
                <p class="mb-1"><strong>Session:</strong> {{ formatDate(selectedBooking.slot.date) }} at {{ selectedBooking.slot.start_time }}</p>
                <p class="mb-0"><strong>Duration:</strong> {{ selectedBooking.duration_minutes }} minutes</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeMeetingLinkModal">
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="updateMeetingLink"
              :disabled="loading"
            >
              {{ loading ? 'Updating...' : 'Update Link' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Backdrop -->
    <div
      v-if="showMeetingLinkModal"
      class="modal-backdrop fade show"
      @click="closeMeetingLinkModal"
    ></div>
  </div>
</template>

<script>
import ConsultancyService from '@/services/ConsultancyService';

export default {
  name: 'AdminConsultancyBookings',
  data() {
    return {
      bookings: [],
      loading: false,
      showMeetingLinkModal: false,
      selectedBooking: null,
      meetingLink: '',
      
      filters: {
        status: '',
        date_from: '',
        date_to: '',
        type: ''
      },

      stats: {
        total_bookings: 0,
        confirmed_bookings: 0,
        pending_bookings: 0,
        today_bookings: 0
      }
    };
  },

  watch: {
    filters: {
      handler() {
        this.fetchBookings();
      },
      deep: true
    }
  },

  mounted() {
    this.fetchBookings();
    this.fetchStats();
  },

  methods: {
    async fetchBookings() {
      this.loading = true;
      try {
        const response = await ConsultancyService.admin.getAllBookings(this.filters);
        this.bookings = response.data?.data || response.data || [];
      } catch (error) {
        console.error('Error fetching bookings:', error);
        this.$toast?.error('Failed to fetch bookings: ' + error.message);
      }
      this.loading = false;
    },

    async fetchStats() {
      try {
        const response = await ConsultancyService.admin.getStats();
        this.stats = response.data || {};
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Don't show error for stats as it's not critical
      }
    },

    async confirmBooking(bookingId) {
      try {
        await ConsultancyService.admin.confirmBooking(bookingId);
        this.$toast?.success('Booking confirmed successfully!');
        this.fetchBookings();
        this.fetchStats();
      } catch (error) {
        console.error('Error confirming booking:', error);
        this.$toast?.error('Failed to confirm booking: ' + error.message);
      }
    },

    async cancelBooking(bookingId) {
      if (!confirm('Are you sure you want to cancel this booking?')) return;
      
      try {
        await ConsultancyService.admin.cancelBooking(bookingId, 'Cancelled by admin');
        this.$toast?.success('Booking cancelled successfully!');
        this.fetchBookings();
        this.fetchStats();
      } catch (error) {
        console.error('Error cancelling booking:', error);
        this.$toast?.error('Failed to cancel booking: ' + error.message);
      }
    },

    openMeetingLinkModal(booking) {
      this.selectedBooking = booking;
      this.meetingLink = booking.meeting_link || '';
      this.showMeetingLinkModal = true;
    },

    closeMeetingLinkModal() {
      this.showMeetingLinkModal = false;
      this.selectedBooking = null;
      this.meetingLink = '';
    },

    async updateMeetingLink() {
      this.loading = true;
      try {
        await ConsultancyService.admin.updateMeetingLink(this.selectedBooking._id, this.meetingLink);
        this.$toast?.success('Meeting link updated successfully!');
        this.fetchBookings();
        this.closeMeetingLinkModal();
      } catch (error) {
        console.error('Error updating meeting link:', error);
        this.$toast?.error('Failed to update meeting link: ' + error.message);
      }
      this.loading = false;
    },

    async sendMeetingLink(bookingId) {
      try {
        await ConsultancyService.admin.sendMeetingLink(bookingId);
        const booking = this.bookings.find(b => b._id === bookingId);
        if (booking) {
          this.$toast?.success(`Meeting link sent to ${booking.user.email}`);
        }
      } catch (error) {
        console.error('Error sending meeting link:', error);
        this.$toast?.error('Failed to send meeting link: ' + error.message);
      }
    },

    getStatusColor(status) {
      const colors = {
        confirmed: 'bg-success',
        pending: 'bg-warning',
        cancelled: 'bg-danger',
        completed: 'bg-primary'
      };
      return colors[status] || 'bg-secondary';
    },

    getPackageColor(packageName) {
      const colors = {
        bronze: 'bg-warning text-dark',
        silver: 'bg-secondary',
        gold: 'bg-warning text-dark',
        platinum: 'bg-purple text-white'
      };
      return colors[packageName?.toLowerCase()] || 'bg-secondary';
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString();
    }
  }
};
</script>

<style scoped>
.bg-purple {
  background-color: #6f42c1 !important;
}

.modal.show {
  opacity: 1;
}

.consultancy-bookings-management {
  padding: 1rem;
}

.card {
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.badge {
  font-size: 0.75rem;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.text-break {
  word-break: break-all;
}

.gap-2 {
  gap: 0.5rem !important;
}

.btn-sm {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size: 0.875rem;
}
</style>